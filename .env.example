# OpenAI API Configuration
OPENAI_API_KEY=openai_api_key_here

# LLM Configuration
# Option 1: Direct OpenAI API (default for local development)
# LLM_TYPE=OPENAI
# LLM_MODEL=gpt-4o
# LLM_MODEL_SECOND_AGENT=gpt-4.1

# Option 2: Azure OpenAI Configuration (Model as a Service - MaaS)
# This is the default configuration for Paychex internal environments
LLM_TYPE=AZURE
AZURE_ENDPOINT=https://service-internal-n2a.paychex.com
API_VERSION=2024-02-01
# When using Paychex MaaS, the system will automatically format the endpoint as:
# {AZURE_ENDPOINT}/eps/shared/azure/openai/deployments/{model}

# LangSmith API Configuration
LANGCHAIN_API_KEY=langsmith_api_key_here
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT="https://api.smith.langchain.com"
LANGCHAIN_PROJECT="payroll-email-agent"

# MCP Configuration
# These values are already defined in the docker-compose.yml for this service.
# For a HTTP-based server: 
# MCP_SERVERS__PAYCHEX__URL="http://localhost:9000/mcp"
# MCP_SERVERS__PAYCHEX__TRANSPORT="streamable_http"
# MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL="http://localhost:9000/auth/token" #uncomment if using a local run of the server

# New OAuth creds for that server
# NOTE: There is no need to add MCP_SERVERS__PAYCHEX__CLIENT_ID and MCP_SERVERS__PAYCHEX__CLIENT_SECRET to your .env file, as they are already defined in the docker-compose.yml for this service.
# If you need to use a different client_id and client_secret, you can add them here but the should match the ones defined in the payroll-mcp-servers .env file.
# MCP_SERVERS__PAYCHEX__CLIENT_ID="payroll_agent"
# MCP_SERVERS__PAYCHEX__CLIENT_SECRET="key_here"

# MCP TESTING OAuth CA API uncomment if you need to use the CA API for testing
#PAYCHEX_CLIENT_ID="<your_client_id_here>"
#PAYCHEX_CLIENT_SECRET="<your_client_secret_here>"