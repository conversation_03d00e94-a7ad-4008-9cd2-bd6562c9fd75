#!/usr/bin/env python3
"""
Test script for Paychex OAuth token endpoint and API calls

HOW TO RUN:
    python tests/test_paychex_api_oauth.py

SETUP:
    1. Set environment variables:
       export PAYCHEX_CLIENT_ID="your_client_id"
       export PAYCHEX_CLIENT_SECRET="your_client_secret"

    2. OR create/update .env file in project root:
       PAYCHEX_CLIENT_ID=your_client_id
       PAYCHEX_CLIENT_SECRET=your_client_secret

    3. See .env.example for full configuration options

WHAT IT TESTS:
    - OAuth client credentials flow
    - Token retrieval and validation
    - API endpoint testing with obtained token
    - Comprehensive integration testing
"""

import requests
import json

from os import environ
from typing import Dict, Any
from config import get_paychex_credentials, get_api_urls, check_credentials


def test_oauth_token() -> Dict[str, Any]:
    """
    Test the OAuth client credentials flow
    """
    # Use the working OAuth token URL
    url = "https://oidc.n2a.paychex.com/oauth/token"

    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }

    # Get credentials from configuration
    client_id, client_secret = get_paychex_credentials()

    if not client_id or not client_secret:
        return {
            "success": False,
            "error": "Missing environment variables: PAYCHEX_CLIENT_ID and/or PAYCHEX_CLIENT_SECRET"
        }

    data = {
        "grant_type": "client_credentials",
        "client_id": environ.get("PAYCHEX_CLIENT_ID", client_id),
        "client_secret": environ.get("PAYCHEX_CLIENT_SECRET", client_secret),
    }

    try:
        print("🚀 Testing OAuth token endpoint...")
        print(f"URL: {url}")
        print(f"Data: {data}")
        print("-" * 50)

        response = requests.post(url, headers=headers, data=data, timeout=30)

        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print("-" * 50)

        if response.status_code == 200:
            token_data = response.json()
            print("✅ SUCCESS! Token received:")
            print(json.dumps(token_data, indent=2))

            # Extract useful info
            access_token = token_data.get("access_token", "")
            token_type = token_data.get("token_type", "")
            expires_in = token_data.get("expires_in", "")

            print(f"\n📋 Token Summary:")
            print(f"  Token Type: {token_type}")
            print(f"  Expires In: {expires_in} seconds")
            print(f"  Access Token (first 20 chars): {access_token[:20]}...")

            return {
                "success": True,
                "status_code": response.status_code,
                "token_data": token_data
            }
        else:
            print("❌ FAILED!")
            print(f"Response: {response.text}")
            return {
                "success": False,
                "status_code": response.status_code,
                "error": response.text
            }

    except requests.exceptions.RequestException as e:
        print(f"❌ REQUEST ERROR: {e}")
        return {
            "success": False,
            "error": str(e)
        }
    except json.JSONDecodeError as e:
        print(f"❌ JSON DECODE ERROR: {e}")
        print(f"Raw response: {response.text}")
        return {
            "success": False,
            "error": f"Invalid JSON response: {e}"
        }


def test_api_endpoints_with_token(access_token: str) -> Dict[str, Any]:
    """
    Test various API endpoints with the obtained token
    """
    # Use only the working base URL
    api_bases = [
        "https://partner.n2a.paychex.com"
    ]

    # API endpoints to test
    endpoints = [
        "/companies",
    ]

    # Use the working Accept header
    accept_header = "application/vnd.paychex.companies.v1+json"

    results = {}

    print("\n🔍 Testing API endpoints with token...")
    print("-" * 60)

    for base_url in api_bases:
        print(f"\n🌐 Testing base URL: {base_url}")
        results[base_url] = {}

        for endpoint in endpoints:
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Accept": accept_header,
                "Content-Type": "application/json"
            }

            full_url = f"{base_url}{endpoint}"
            try:
                print(f"  📡 Testing endpoint: {endpoint}")
                response = requests.get(full_url, headers=headers, timeout=15)

                result_info = {
                    "status_code": response.status_code,
                    "success": response.status_code == 200,
                    "response_preview": response.text[:200] if response.text else ""
                }

                if response.status_code == 200:
                    print(f"    ✅ {response.status_code} - SUCCESS!")
                    try:
                        data = response.json()
                        result_info["json_response"] = data
                        print(f"    📋 Response: {json.dumps(data, indent=2)[:100]}...")
                    except json.JSONDecodeError:
                        print(f"    📄 Text response: {response.text[:100]}...")
                elif response.status_code == 401:
                    print(f"    🔐 {response.status_code} - Unauthorized (token issue)")
                elif response.status_code == 403:
                    print(f"    🚫 {response.status_code} - Forbidden")
                elif response.status_code == 404:
                    print(f"    ❌ {response.status_code} - Not Found")
                else:
                    print(f"    ⚠️  {response.status_code} - {response.text[:50]}...")

                results[base_url][endpoint] = result_info

            except requests.exceptions.RequestException as e:
                print(f"    💥 Error: {e}")
                results[base_url][endpoint] = {
                    "error": str(e),
                    "success": False
                }

    return results




if __name__ == "__main__":
    print("🚀 Testing Paychex OAuth + API Integration")
    print("=" * 60)

    # Step 1: Get OAuth token
    print("\n1️⃣ Getting OAuth token...")
    token_result = test_oauth_token()

    if token_result["success"]:
        print("\n🎉 OAuth token obtained successfully!")
        access_token = token_result["token_data"]["access_token"]

        # Step 2: Test API endpoints with token
        print("\n2️⃣ Testing API endpoints with token...")
        api_results = test_api_endpoints_with_token(access_token)

        # Step 3: Summary
        print("\n" + "=" * 60)
        print("📋 SUMMARY")
        print("=" * 60)

        successful_endpoints = []
        for base_url, endpoints in api_results.items():
            for endpoint, result in endpoints.items():
                if result.get("success"):
                    successful_endpoints.append(f"{base_url}{endpoint}")

        if successful_endpoints:
            print("✅ Working API endpoints found:")
            for endpoint in successful_endpoints:
                print(f"  • {endpoint}")
        else:
            print("❌ No working API endpoints found")
            print("\n🔍 Debugging info:")
            print("- Token obtained successfully ✅")
            print("- All API endpoints returned errors ❌")
            print("- Check if the correct API base URL is being used")
            print("- Verify token permissions/scopes")

    else:
        print("\n💥 OAuth token failed - cannot test API endpoints")
        exit(1)
