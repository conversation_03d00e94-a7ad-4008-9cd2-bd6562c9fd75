from unittest.mock import patch

from fastapi.testclient import TestClient

from app.main import app

client = TestClient(app)

def test_root_endpoint():
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "Welcome to the Payroll Email Agent API"}

@patch("app.api.routes.graph")
def test_process_email_endpoint(mock_graph):
    # Mock the graph response
    fake_result = {
        "payroll_entries": [],
        "summary": {},
        "validation_status": "pending",
    }
    mock_graph.invoke.return_value = fake_result

    payload = {
        "content": "Test email content",
        "sender": "<EMAIL>",
        "subject": "Test Subject",
    }
    resp = client.post("/api/v1/process-email", json=payload)

    assert resp.status_code == 200
    expected = {**fake_result, "status": "success"}
    assert resp.json() == expected
    mock_graph.invoke.assert_called_once()

@patch("app.api.routes.graph")
def test_process_email_initial_state(mock_graph) -> None:
    """Ensure the route builds and forwards the correct AgentState."""
    mock_graph.invoke.return_value = {
        "payroll_entries": [],
        "summary": {},
        "validation_status": "pending",
    }

    payload = {
        "content": "Hello",
        "sender": "<EMAIL>",
        "subject": "Subject",
    }

    resp = client.post("/api/v1/process-email", json=payload)
    assert resp.status_code == 200

    # Validate first argument passed to graph.invoke
    mock_graph.invoke.assert_called_once()
    (first_arg,) = mock_graph.invoke.call_args.args  # positional arg 0

    # It should be a dict coming from AgentState.model_dump()
    assert isinstance(first_arg, dict)
    assert first_arg["input"]["content"] == payload["content"]
    assert first_arg["input"]["sender"] == payload["sender"]
    assert first_arg["input"]["subject"] == payload["subject"]
    assert first_arg["processing_status"]["stage"] == "digesting"

@patch("app.api.routes.graph")
def test_process_email_graph_exception(mock_graph) -> None:
    """If graph.invoke raises, the route should respond with HTTP 500."""
    mock_graph.invoke.side_effect = RuntimeError("boom")

    good_payload = {
        "content": "irrelevant",
        "sender": "<EMAIL>",
        "subject": "Subject",
    }

    resp = client.post("/api/v1/process-email", json=good_payload)

    assert resp.status_code == 500
    body = resp.json()
    assert body["detail"]["error"] == "boom"
    assert body["detail"]["stage"] == "error"
    assert body["detail"]["message"] == "Failed to process payroll email"

def test_process_email_bad_request() -> None:
    """FastAPI should return 422 when the payload is incomplete."""
    # `content` is missing on purpose
    bad_payload = {"sender": "<EMAIL>", "subject": "No content"}

    resp = client.post("/api/v1/process-email", json=bad_payload)

    assert resp.status_code == 422
    # FastAPI always returns a 'detail' list for validation errors
    assert "detail" in resp.json()


if __name__ == "__main__":
    import unittest
    unittest.main()
