{"cells": [{"cell_type": "code", "execution_count": null, "id": "3c1b67b9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting requests\n", "  Downloading requests-2.32.4-py3-none-any.whl.metadata (4.9 kB)\n", "Collecting charset_normalizer<4,>=2 (from requests)\n", "  Downloading charset_normalizer-3.4.2-cp313-cp313-win_amd64.whl.metadata (36 kB)\n", "Collecting idna<4,>=2.5 (from requests)\n", "  Downloading idna-3.10-py3-none-any.whl.metadata (10 kB)\n", "Collecting urllib3<3,>=1.21.1 (from requests)\n", "  Downloading urllib3-2.4.0-py3-none-any.whl.metadata (6.5 kB)\n", "Collecting certifi>=2017.4.17 (from requests)\n", "  Downloading certifi-2025.6.15-py3-none-any.whl.metadata (2.4 kB)\n", "Downloading requests-2.32.4-py3-none-any.whl (64 kB)\n", "Downloading charset_normalizer-3.4.2-cp313-cp313-win_amd64.whl (105 kB)\n", "Downloading idna-3.10-py3-none-any.whl (70 kB)\n", "Downloading urllib3-2.4.0-py3-none-any.whl (128 kB)\n", "Downloading certifi-2025.6.15-py3-none-any.whl (157 kB)\n", "Installing collected packages: urllib3, idna, charset_normalizer, certifi, requests\n", "\n", "   ---------------------------------------- 0/5 [urllib3]\n", "   ---------------------------------------- 0/5 [urllib3]\n", "   ---------------------------------------- 0/5 [urllib3]\n", "   -------- ------------------------------- 1/5 [idna]\n", "   ---------------- ----------------------- 2/5 [charset_normalizer]\n", "   -------------------------------- ------- 4/5 [requests]\n", "   -------------------------------- ------- 4/5 [requests]\n", "   ---------------------------------------- 5/5 [requests]\n", "\n", "Successfully installed certifi-2025.6.15 charset_normalizer-3.4.2 idna-3.10 requests-2.32.4 urllib3-2.4.0\n", "Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  WARNING: The script normalizer.exe is installed in 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts' which is not on PATH.\n", "  Consider adding this directory to PATH or, if you prefer to suppress this warning, use --no-warn-script-location.\n"]}], "source": ["#%pip install requests"]}, {"cell_type": "code", "execution_count": 1, "id": "71eb9210", "metadata": {}, "outputs": [], "source": ["import requests\n", "import time\n", "import os\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "2eb9c59c", "metadata": {}, "outputs": [], "source": ["# LLM API URL and key\n", "llm_base_url = \"https://service-internal-n2a.paychex.com\"\n", "llm_endpoint = \"/eps/shared/azure/openai/deployments/gpt-4o\"\n", "api_key = \"\"  # Replace with your actual key\n", "\n", "# Full URL\n", "api_url = f\"{llm_base_url}{llm_endpoint}\"\n", "api_version = \"2023-05-15\"\n", "\n", "# Simple test prompt\n", "test_prompt = \"Hello, can you respond with a short message to confirm you're working?\"\n", "\n", "# Prepare headers and payload\n", "headers = {\n", "    \"Authorization\": f\"Bearer {api_key}\",\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "payload = {\n", "    \"model\": \"gpt-4o\",  # You can change this to the model you want to test\n", "    \"messages\": [{\"role\": \"user\", \"content\": test_prompt}],\n", "    \"max_tokens\": 100,\n", "    \"api_version\": api_version\n", "}\n", "\n", "query_params = {\n", "    \"api_version\": \"2023-05-15\"\n", "}\n"]}, {"cell_type": "code", "execution_count": null, "id": "d6537363", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Testing Azure OpenAI API directly (without LangChain)...\n", "--------------------------------------------------\n", "Sending request to: https://service-internal-n2a.paychex.com/eps/shared/azure/openai/deployments/gpt-4o/chat/completions?api-version=2024-02-01\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\urllib3\\connectionpool.py:1097: InsecureRequestWarning: Unverified HTTPS request is being made to host 'service-internal-n2a.paychex.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Status Code: 200\n", "Response Time: 0.87 seconds\n", "--------------------------------------------------\n", "✅ Success! Azure OpenAI API is working.\n", "Response:\n", "{\n", "  \"choices\": [\n", "    {\n", "      \"content_filter_results\": {\n", "        \"hate\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        },\n", "        \"self_harm\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        },\n", "        \"sexual\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        },\n", "        \"violence\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        }\n", "      },\n", "      \"finish_reason\": \"stop\",\n", "      \"index\": 0,\n", "      \"logprobs\": null,\n", "      \"message\": {\n", "        \"annotations\": [],\n", "        \"content\": \"Hello! Yes, I'm here and ready to assist you. How can I help today?\",\n", "        \"refusal\": null,\n", "        \"role\": \"assistant\"\n", "      }\n", "    }\n", "  ],\n", "  \"created\": 1750077809,\n", "  \"id\": \"chatcmpl-Bj3TNXxAc3GeoqiYAjhPwdEfseGWY\",\n", "  \"model\": \"gpt-4o-2024-08-06\",\n", "  \"object\": \"chat.completion\",\n", "  \"prompt_filter_results\": [\n", "    {\n", "      \"prompt_index\": 0,\n", "      \"content_filter_results\": {\n", "        \"hate\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        },\n", "        \"jailbreak\": {\n", "          \"filtered\": false,\n", "          \"detected\": false\n", "        },\n", "        \"self_harm\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        },\n", "        \"sexual\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        },\n", "        \"violence\": {\n", "          \"filtered\": false,\n", "          \"severity\": \"safe\"\n", "        }\n", "      }\n", "    }\n", "  ],\n", "  \"system_fingerprint\": \"fp_ee1d74bde0\",\n", "  \"usage\": {\n", "    \"completion_tokens\": 19,\n", "    \"completion_tokens_details\": {\n", "      \"accepted_prediction_tokens\": 0,\n", "      \"audio_tokens\": 0,\n", "      \"reasoning_tokens\": 0,\n", "      \"rejected_prediction_tokens\": 0\n", "    },\n", "    \"prompt_tokens\": 29,\n", "    \"prompt_tokens_details\": {\n", "      \"audio_tokens\": 0,\n", "      \"cached_tokens\": 0\n", "    },\n", "    \"total_tokens\": 48\n", "  }\n", "}\n", "\n", "Assistant's response:\n", "Hello! Yes, I'm here and ready to assist you. How can I help today?\n", "--------------------------------------------------\n"]}], "source": ["def test_azure_openai_direct():\n", "    \"\"\"Test Azure OpenAI API directly without LangChain\"\"\"\n", "    print(\"Testing Azure OpenAI API directly (without LangChain)...\")\n", "    print(\"-\" * 50)\n", "    \n", "    # Configuration\n", "    api_key = os.getenv(\"CONSUMER_API_KEY\", api_key)\n", "    model = \"gpt-4o\"\n", "    azure_endpoint = \"https://service-internal-n2a.paychex.com/eps/shared/azure\"\n", "    api_version = \"2024-02-01\"\n", "    \n", "    # Construct the full URL\n", "    url = f\"{azure_endpoint}/openai/deployments/{model}/chat/completions?api-version={api_version}\"\n", "    \n", "    # Headers\n", "    headers = {\n", "        \"Content-Type\": \"application/json\",\n", "        \"api-key\": api_key\n", "    }\n", "    \n", "    # Request payload\n", "    payload = {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"system\",\n", "                \"content\": \"You are a helpful assistant that provides brief responses.\"\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": \"Hello, can you confirm you're working?\"\n", "            }\n", "        ],\n", "        \"max_tokens\": 100\n", "    }\n", "    \n", "    print(f\"Sending request to: {url}\")\n", "    start_time = time.time()\n", "    \n", "    try:\n", "        # Make the API call\n", "        response = requests.post(\n", "            url,\n", "            headers=headers,\n", "            json=payload,\n", "            timeout=30,\n", "            verify=False  # Disable SSL verification for internal endpoints\n", "        )\n", "        \n", "        elapsed_time = time.time() - start_time\n", "        \n", "        print(f\"Status Code: {response.status_code}\")\n", "        print(f\"Response Time: {elapsed_time:.2f} seconds\")\n", "        print(\"-\" * 50)\n", "        \n", "        if response.status_code == 200:\n", "            print(\"✅ Success! Azure OpenAI API is working.\")\n", "            result = response.json()\n", "            print(\"Response:\")\n", "            print(json.dumps(result, indent=2))\n", "            \n", "            # Extract just the response content\n", "            if \"choices\" in result and len(result[\"choices\"]) > 0:\n", "                content = result[\"choices\"][0][\"message\"][\"content\"]\n", "                print(\"\\nAssistant's response:\")\n", "                print(content)\n", "        else:\n", "            print(\"⚠️ API responded with non-200 status code.\")\n", "            print(f\"Response: {response.text}\")\n", "            \n", "    except Exception as e:\n", "        elapsed_time = time.time() - start_time\n", "        print(f\"❌ Error: {e}\")\n", "        print(f\"Elapsed Time: {elapsed_time:.2f} seconds\")\n", "    \n", "    print(\"-\" * 50)\n", "\n", "# Run the direct test\n", "test_azure_openai_direct()\n"]}, {"cell_type": "code", "execution_count": null, "id": "fc3d58bc", "metadata": {}, "outputs": [], "source": ["def test_token_limit(token_count, model=\"gpt-4o\"):\n", "    \"\"\"Test Azure OpenAI API with increasing token counts to find the limit\"\"\"\n", "    print(f\"Testing with approximately {token_count} tokens...\")\n", "    print(\"-\" * 50)\n", "    \n", "    # Configuration\n", "    api_key = os.getenv(\"CONSUMER_API_KEY\", api_key)\n", "    azure_endpoint = \"https://service-internal-n2a.paychex.com/eps/shared/azure\"\n", "    api_version = \"2024-02-01\"\n", "    \n", "    # Construct the full URL\n", "    url = f\"{azure_endpoint}/openai/deployments/{model}/chat/completions?api-version={api_version}\"\n", "    \n", "    # Headers\n", "    headers = {\n", "        \"Content-Type\": \"application/json\",\n", "        \"api-key\": api_key\n", "    }\n", "    \n", "    # Create a long prompt by repeating text\n", "    # Each word is roughly 1.3 tokens on average\n", "    words_needed = int(token_count / 1.3)\n", "    long_text = \" \".join([\"test\"] * words_needed)\n", "    \n", "    # Request payload\n", "    payload = {\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"system\",\n", "                \"content\": \"You are a helpful assistant.\"\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": f\"This is a token limit test with approximately {token_count} tokens: {long_text}\"\n", "            }\n", "        ],\n", "        \"max_tokens\": 100\n", "    }\n", "    \n", "    print(f\"Sending request to: {url}\")\n", "    print(f\"Approximate input tokens: {token_count}\")\n", "    start_time = time.time()\n", "    \n", "    try:\n", "        # Make the API call\n", "        response = requests.post(\n", "            url,\n", "            headers=headers,\n", "            json=payload,\n", "            timeout=60,  # Longer timeout for large requests\n", "            verify=False  # Disable SSL verification for internal endpoints\n", "        )\n", "        \n", "        elapsed_time = time.time() - start_time\n", "        \n", "        print(f\"Status Code: {response.status_code}\")\n", "        print(f\"Response Time: {elapsed_time:.2f} seconds\")\n", "        \n", "        if response.status_code == 200:\n", "            print(\"✅ Success! Request processed successfully.\")\n", "            result = response.json()\n", "            \n", "            # Check if the response includes token usage information\n", "            if \"usage\" in result:\n", "                print(f\"Actual tokens used: {result['usage']}\")\n", "            \n", "            return True, elapsed_time, response.status_code, result.get(\"usage\", {})\n", "        else:\n", "            print(\"⚠️ API responded with non-200 status code.\")\n", "            print(f\"Response: {response.text}\")\n", "            return False, elapsed_time, response.status_code, {}\n", "            \n", "    except Exception as e:\n", "        elapsed_time = time.time() - start_time\n", "        print(f\"❌ Error: {e}\")\n", "        print(f\"Elapsed Time: {elapsed_time:.2f} seconds\")\n", "        return False, elapsed_time, 0, {}\n", "    \n", "    finally:\n", "        print(\"-\" * 50)"]}, {"cell_type": "code", "execution_count": null, "id": "a1679465", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "916d345f", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c5e77f3d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d33c9847", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1cc3a893", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "879abeb4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9027b659", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}