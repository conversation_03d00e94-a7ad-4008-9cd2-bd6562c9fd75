[project]
name = "payroll-email-agent"
version = "0.1.0"
description = "A LangGraph-based email agent for payroll processing"
authors = [
    {name = "Email", email = "<EMAIL>"}
]
requires-python = ">=3.11"
readme = "README.md"
dependencies = [
    "fastapi>=0.115.12",
    "uvicorn>=0.27.0",
    "langgraph>=0.3.30",
    "langgraph-api>=0.0.33",
    "langchain>=0.3.23",
    "langchain-community>=0.3.21",
    "langchain-openai>=0.3.13",
    "langchain-core>=0.3.52",
    "openai>=1.74.0",
    "httpx>=0.28.1",
    "markdownify>=1.1.0",
    "python-dotenv==1.0.1",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "sse-starlette>=2.1.3",
    "colorama>=0.4.6",
    "pandas",
    "tqdm",
    "openpyxl",
    "langchain-mcp-adapters>=0.1.0",
]

[project.optional-dependencies]
test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.28.1",
    "pytest-cov>=4.1.0"
]

dev = [
    "pre-commit>=3.7.0",
    "ruff>=0.4.3"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.ruff]
line-length = 88
target-version = "py39"

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
