## Testing in LangGraph Playground

You can test both the full graph and individual subgraphs using LangGraph Studio.

### 1. Configure `langgraph.json`

Ensure you have all subgraphs listed in your project root:

```json
{
  "python_version": "3.11",
  "env": "./.env",
  "dependencies": ["."],
  "graphs": {
    "payroll_email_agent": "./app/payroll_agent/graph/graph_builder.py:graph",
    "account_lookup": "./app/payroll_agent/graph/graph_builder.py:account_lookup",
    "classification": "./app/payroll_agent/graph/graph_builder.py:classification",
    "payroll_extraction": "./app/payroll_agent/graph/graph_builder.py:payroll_extraction",
    "execution": "./app/payroll_agent/graph/graph_builder.py:execution",
    "confirmation_output": "./app/payroll_agent/graph/graph_builder.py:confirmation_output"
  }
}
```

---

### 2. Start the Playground

From your project root, run:

```bash
uvx --refresh --from "langgraph-cli[inmem]" --with-editable . langgraph dev
```

This will:
- Start the LangGraph Studio UI
- Automatically detect the graphs listed in `langgraph.json`

---

### 3. Test a Graph

1. Open the Studio UI (usually at [https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024](https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024))
2. Use the dropdown at the top to choose:
   - `payroll_email_agent` to test the full pipeline
   - `account_lookup`, `execution`, etc. to test subgraphs individually
3. Paste or load input state for the graph
4. Click **Run** to step through the flow interactively

#### Example: Testing account_lookup SubGraph:

![Subgraph Testing Example](./img/test_subgraph.png)


### Input Requirements by Subgraph

| Subgraph              | Required Fields in Input Panel             |
|-----------------------|--------------------------------------------|
| `account_lookup`              | AgentState                           |
| `classification`      |  AgentState (placeholder)          |
| `payroll_extraction`      |  AgentState (placeholder)          |
| `execution`           |  AgentState (placeholder)    |
| `confirmation_output` | ConfirmationInputState      |

---

### Notes

- **Subgraph crashes with `NoneType` error:**  
  Ensure required nested fields are present in your input (e.g., `client_config.payroll_format`).

- **Changes not reflected in Studio:**  
  Run `uvx --refresh` and restart the server.