import uuid
import asyncio

from datetime import datetime
from fastapi import APIRouter, HTTPException, Request, Depends, Head<PERSON>
from concurrent.futures import ThreadPoolExecutor

from app.api.models.input import UpstreamModel
from app.api.models.output import EmailResponse
from app.payroll_agent.graph.states.classification import InputState
from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.graph.graph_builder import graph
from app.payroll_agent.utils.funcs import format_payroll_state_response, parse_upstream_model
from app.payroll_agent.utils.dashboard import dashboard_integration
from app.cli.logging_utils import setup_logger


logger = setup_logger('api.routes')
router = APIRouter()


@router.post("/process-email", response_model=EmailResponse)
async def process_email(request: UpstreamModel):
    logger.info(f"Received process_email request")

    started_at = datetime.utcnow().isoformat()

    try:
        # parse the request model for the graph
        logger.debug(f"Request model: {request.model_dump()}")
        input_dict = parse_upstream_model(request)

        input_state = InputState(**input_dict)

        # Initialize the graph state with Pydantic model
        input_state = PayrollState(input_state=input_state)

        logger.debug(f"Initialized InputState: {input_state!r}")
        logger.info(f"Invoking graph.invoke")
        result_state = await graph.ainvoke(input_state)
        logger.debug(f"account_lookup.invoke result: {result_state}")
        finished_at = datetime.utcnow().isoformat()

        try:
            def run_dashboard_logging():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        return loop.run_until_complete(
                            dashboard_integration.log_transaction(result_state, started_at, finished_at)
                        )
                    finally:
                        loop.close()
                except Exception as e:
                    logger.error(f"Dashboard logging failed: {e}")
                    return False

            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(run_dashboard_logging)
                dashboard_success = future.result(timeout=10)

            if dashboard_success:
                logger.info("Dashboard logging completed successfully")
            else:
                logger.warning("Dashboard logging failed")

        except Exception as e:
            logger.error(f"Dashboard logging error: {e}")

        plain_dict = format_payroll_state_response(result_state)

        plain_dict["started_at"] = started_at
        plain_dict["finished_at"] = finished_at

        response = EmailResponse(response=plain_dict)
        logger.debug(f"process_email completed successfully, returning response: {response}")
        return response

    except Exception as e:
        logger.error(f"Failed to process payroll email, error: {type(e).__name__} - {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error": str(e),
                "stage": "process_email",
                "message": "Failed to process payroll email"
            }
        )


@router.post("/payroll-ai-agent-ingest")
async def payroll_ai_agent_ingest(request: Request):
    """
    Endpoint for receiving payroll AI agent data.
    Simply logs the request and returns a confirmation response.
    
    Error codes:
    - 200: Email accepted and queued
    - 400: Missing fields or malformed JSON
    - 401: Unauthorized (bad or missing token)
    - 409: Duplicate email_id already processed
    - 500: Server error — bot should retry with exponential backoff
    """
    try:
        # Log the request headers
        logger.info(f"Received payroll-ai-agent-ingest request with headers: {request.headers}")
        
        # Try to parse the body as JSON, but handle empty or invalid JSON
        try:
            body = await request.json()
            logger.info(f"Request body: {body}")
        except Exception as json_error:
            logger.warning(f"Could not parse request body as JSON: {str(json_error)}")
            body = {}  # Use empty dict if JSON parsing fails
        
        # Generate a unique processing ID
        processing_id = f"proc-{uuid.uuid4().hex[:6]}"
        
        # Return the expected response format
        return {
            "status": "received",
            "comm_mining_ID": body.get("comm_mining_ID", f"{uuid.uuid4().hex[:16]}"),
            "attachment": True,
            "received_at": datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%SZ"),
            "processing_id": processing_id
        }
    except Exception as e:
        logger.error(f"Error processing payroll-ai-agent-ingest request: {str(e)}")
        raise HTTPException(status_code=500, detail="Server error")
