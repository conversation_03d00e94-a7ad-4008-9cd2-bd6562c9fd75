import json
import async<PERSON>
from concurrent.futures import Thread<PERSON>oolExecutor

from app.cli.logging_utils import setup_logger
from app.payroll_agent.config.config import settings
from app.payroll_agent.utils.prompt import load_prompt
from app.payroll_agent.utils.funcs import load_routers_config

from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.models.release import ProceedWithoutVerificationMetadata
from app.payroll_agent.utils.dashboard import DashboardIntegration


# Set up the logger
logger = setup_logger(__name__)

PROMPTS = load_prompt("release")
ROUTERS_CONFIG = load_routers_config('routers_config')['release']
dashboard = DashboardIntegration()


def release_proceed_without_verification(state: PayrollState) -> PayrollState:
    """Determine if payroll can be released without client verification."""
    logger.info("release_proceed_without_verification called...")
    try:
        llm = settings.LLM().with_structured_output(schema=ProceedWithoutVerificationMetadata, method="function_calling")

        prompt = PROMPTS["proceed_without_validation"].format(RAW_EMAIL_TEXT=state.input_state.EmailContent)
        payrolls = state.execution_output_state.completed_payrolls
        payrolls = json.dumps(payrolls)

        message = [
            {"role": "system", "content": prompt},
            {"role": "user", "content":payrolls}
        ]
        logger.debug(f"LLM prompt for release_proceed_without_verification: {message}")

        # Invoke the LLM with the prompt
        result = llm.invoke(message)
        logger.info(f"release_proceed_without_verification got structured output: {result}")

        # Add the result to the state
        state.release_output_state.proceed_without_verification = result
        logger.debug(f"release_proceed_without_verification returning PayrollState: {state}")

        return state
    except Exception as e:
        logger.error(f"Error in release_proceed_without_verification {type(e).__name__} - {e}", exc_info=True)
        raise


def release_payroll(state: PayrollState) -> PayrollState:
    """Generate a string describing each field with optional/required label and description."""
    logger.info("release_payroll called...")
    state.release_output_state.release_payroll = True
    return state


def release_router(state: PayrollState) -> PayrollState:
    """Validate the sender information and check if it matches the expected format."""
    logger.info("release_router called...")
    #check all conditions are met
    status = state.release_output_state.proceed_without_verification.status
    errors = state.release_output_state.proceed_without_verification.errors
    if status:
        state.release_output_state.should_continue = True
        logger.info("Release validation process successful")
    else:
        error_message = f"Payrolls requires validation, status: {errors} "
        logger.warning(error_message)
        state.release_output_state.termination_reason = error_message
    return state


def release_dashboard_logging(state: PayrollState) -> PayrollState:
    """Log the release information to the dashboard."""
    logger.info("release_dashboard_logging called...")

    try:
        started_at = None
        finished_at = None

        async def log_to_dashboard():
            return await dashboard.log_transaction(state, started_at, finished_at)

        def run_logging():
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    result = loop.run_until_complete(log_to_dashboard())
                    return result
                finally:
                    loop.close()
            except Exception as e:
                logger.error(f"Async logging failed: {e}")
                return False

        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(run_logging)
            success = future.result(timeout=15)

        state.release_output_state.release_dashboard_logging = success

        if success:
            logger.info("Dashboard logging completed successfully")
        else:
            logger.warning("Dashboard logging failed")

    except Exception as e:
        logger.error(f"Dashboard logging error: {e}", exc_info=True)
        state.release_output_state.release_dashboard_logging = False

    logger.debug(f"Dashboard logging status: {state.release_output_state.release_dashboard_logging}")
    return state


def release_update_upstream_ticket(state: PayrollState) -> PayrollState:
    """Update the upstream ticket with the release information."""
    logger.info("release_update_upstream_ticket called...")
    # Placeholder for actual ticket update logic
    # This could involve sending data to a ticketing system or API
    state.release_output_state.release_update_upstream_ticket = True
    logger.debug(f"Updating upstream ticket with release information: {state.release_output_state}")
    return state


def release_send_confirmation(state: PayrollState) -> PayrollState:
    """Send a confirmation email to the client."""
    logger.info("release_send_confirmation called...")
    # Placeholder for actual email sending logic
    # This could involve using an email service or SMTP server
    logger.debug(f"Sending confirmation email with release information: {state.release_output_state}")
    state.release_output_state.release_send_confirmation = True
    return state


def release_create_summary(state: PayrollState) -> PayrollState:
    """Create a summary of the release information."""
    logger.info("release_create_summary called...")
    try:
        llm = settings.LLM()

        prompt = PROMPTS["summary"]
        message = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": json.dumps(state.model_dump())}
        ]
        logger.debug(f"LLM prompt for release_create_summary: {message}")

        # Invoke the LLM with the prompt
        result = llm.invoke(message)
        logger.debug(f"release_create_summary got structured output: {result.content}")

        # Add the result to the state
        state.release_output_state.release_create_summary = result.content
        logger.debug(f"release_create_summary returning PayrollState: {state}")
        return state
    except Exception as e:
        logger.error(f"Error in release_create_summary: {type(e).__name__} - {e}", exc_info=True)
        raise


def release_terminate(state: PayrollState) -> PayrollState:
    """Terminate account_lookup with reason when client ID is invalid."""
    logger.warning("release_terminate called...")
    reason = state.release_output_state.termination_reason
    logger.warning(f"Termination reason: {reason}")

    state.release_output_state.termination_reason = reason
    state.release_output_state.should_continue = False
    state.release_output_state.termination_node = release_terminate.__name__
    logger.debug(f"release_terminate returning PayrollState: {state}")

    return state

def release_finishing_node(state: PayrollState) -> PayrollState:
    logger.info("release_finishing_node called...")
    return state
