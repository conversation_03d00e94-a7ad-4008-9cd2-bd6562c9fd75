import json

from app.cli.logging_utils import setup_logger
from app.payroll_agent.utils.mcp import get_mcp_service
from app.payroll_agent.config.config import settings
from app.payroll_agent.utils.prompt import load_prompt
from app.payroll_agent.utils.funcs import load_routers_config

from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.graph.states.company_worker_lookup import ClientConfig
from app.payroll_agent.models.company_worker_lookup import SenderEmailMetadata, ValidateSenderMetadata, WorkersMatch


# Set up the logger
logger = setup_logger(__name__)

PROMPTS = load_prompt("company_worker_lookup")
ROUTERS_CONFIG = load_routers_config('routers_config')['company_worker_lookup']


def company_worker_lookup_identify_sender(state: PayrollState) -> PayrollState:
    """Identify the sender and extract relevant information from the email."""
    logger.info("company_worker_lookup_identify_sender called...")
    try:
        llm = settings.LLM().with_structured_output(schema=SenderEmailMetadata, method="function_calling")

        prompt = PROMPTS["company_worker_lookup_identify_sender"]
        message = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": state.input_state.EmailContent}
        ]
        logger.debug(f"LLM prompt for company_worker_lookup_identify_sender: {message}")

        # Invoke the LLM with the prompt
        sender_info = llm.invoke(message)
        logger.debug(f"company_worker_lookup_identify_sender got structured output: {sender_info!r}")

        state.company_worker_lookup_output_state.sender_email_metadata = sender_info
        state.company_worker_lookup_output_state.employees_metadata = sender_info.employees_names or []
        logger.debug(f"company_worker_lookup_identify_sender returning PayrollState: {state!r}")

        return state
    except Exception as e:
        logger.error(f"Error in company_worker_lookup_identify_sender: {type(e).__name__} - {e}", exc_info=True)
        raise


def company_worker_lookup_get_sender_metadata_description() -> str:
    """Generate a string describing each field with optional/required label and description."""
    description_lines = []
    for name, field in SenderEmailMetadata.model_fields.items():
        required = field.is_required()
        desc = field.description or "No description"
        status = "required" if required else "optional"
        description_lines.append(f"- `{name}` ({status}): {desc}")
    return "\n".join(description_lines)


def company_worker_lookup_validate_sender(state: PayrollState) -> PayrollState:
    """Validate the sender information and check if it matches the expected format."""
    logger.info("company_worker_lookup_validate_sender called...")
    try:
        llm = settings.LLM().with_structured_output(schema=ValidateSenderMetadata, method="function_calling")

        sender_info = state.company_worker_lookup_output_state.sender_email_metadata.model_dump()
        schema_description = company_worker_lookup_get_sender_metadata_description()

        prompt = PROMPTS["company_worker_lookup_validate_sender"].format(
            sender_info=sender_info,
            sender_schema_description=schema_description
        )
        message = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": state.input_state.EmailContent}
        ]
        logger.debug(f"LLM prompt for company_worker_lookup_validate_sender: {message}")

        # Invoke the LLM with the prompt
        validation_result = llm.invoke(message)
        logger.debug(f"company_worker_lookup_validate_sender got structured output: {validation_result!r}")

        state.company_worker_lookup_output_state.validate_sender_metadata = validation_result
        logger.debug(f"company_worker_lookup_validate_sender returning PayrollState: {state!r}")

        return state
    except Exception as e:
        logger.error(f"Error in  company_worker_lookup_validate_sender - {type(e).__name__} - {e}", exc_info=True)
        raise


async def company_worker_lookup_retrieve_company_info(state: PayrollState) -> PayrollState:
    logger.info("company_worker_lookup_retrieve_company_info called...")
    try:
        mcp_service = get_mcp_service()
        company_id= state.input_state.companyID
        logger.debug(f"Fetching company info for CompanyID: {company_id}")
        response = await mcp_service.call_mcp_tool(
                                                    server_name='paychex',
                                                    tool_name='paychex_company_lookup',
                                                    tool_input={"companyID": company_id}
                                                    )
        # Parse the response and add it to the state
        state.company_worker_lookup_output_state.company_lookup = json.loads(response)
    except Exception as e:
        logger.error(f"Error in company_worker_lookup_retrieve_company_info - {type(e).__name__} - {e}", exc_info=True)
    finally:
        return state


def LLM_search_workers(emailContent: str, fullname_dict: dict) -> WorkersMatch:
    """Search for workers in the email content using LLM."""
    llm = settings.LLM().with_structured_output(schema=WorkersMatch, method="function_calling")

    prompt = PROMPTS["company_worker_lookup_search_workers"]

    data = dict(emailContent = emailContent, availableWorkers=fullname_dict)
    message = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": json.dumps(data)}
    ]
    logger.debug(f"LLM prompt for company_worker_lookup_search_workers: {message}")

    # Invoke the LLM with the prompt
    search_result = llm.invoke(message)
    logger.debug(f"LLM search result: {search_result!r}")

    return search_result


async def company_worker_lookup_retrieve_worker_info(state: PayrollState) -> PayrollState:
    logger.info("company_worker_lookup_retrieve_worker_info called...")
    try:
        mcp_service = get_mcp_service()
        company_id = state.input_state.companyID

        logger.debug(f"Fetching worker info for CompanyID: {company_id}")
        response = await mcp_service.call_mcp_tool(server_name='paychex',
                                                    tool_name='paychex_workers_lookup',
                                                    tool_input={"companyID": company_id})
        # Parse the response
        workers_roster = json.loads(response)
        workers = workers_roster['content']
        state.company_worker_lookup_output_state.workers_roster = workers_roster

        # get full names for llm-search-based
        full_name = lambda x: x['name'].get('givenName', '') + ' ' + x['name'].get('middleName') + ' ' + x['name'].get('familyName', '') \
                        if x['name'].get('middleName')!='' else x['name'].get('givenName', '') + ' ' + x['name'].get('familyName', '')

        fullname_dict = { full_name(worker): i for i, worker in enumerate(workers)}

        ## LLM search
        llm_workers_match = LLM_search_workers(emailContent=state.input_state.EmailContent, fullname_dict=fullname_dict)
        state.company_worker_lookup_output_state.llm_workers_match = llm_workers_match

        ### match llm result with workers roster
        workers_matched = {worker.name: workers[worker.worker_number] for worker in llm_workers_match.workers \
                                                                        if worker.worker_number is not None}
        logger.debug(f"workers_matched: {workers_matched}")
        state.company_worker_lookup_output_state.workers_matched = workers_matched

    except Exception as e:
        logger.error(f"Error in company_worker_lookup_retrieve_worker_info - {type(e).__name__} - {e}", exc_info=True)
    finally:
        return state


async def company_worker_lookup_get_payperiodID(state: PayrollState) -> PayrollState:
    logger.info("company_worker_lookup_get_payperiodID called...")
    try:
        mcp_service = get_mcp_service()
        company_id = state.input_state.companyID

        ## get last 3 pay periods
        logger.debug(f"Fetching last pay periods for CompanyID: {company_id}")
        response = await mcp_service.call_mcp_tool(server_name='paychex',
                                                    tool_name='paychex_company_pay_periods',
                                                    tool_input={"companyID": company_id})

        # Parse the response and add it to the state
        pay_periods = json.loads(response)
        state.company_worker_lookup_output_state.pay_periods = pay_periods

        # Get the unprocessed pay period
        if pay_periods.get('content', None):
            pay_periods = pay_periods['content']
            for pay_period in pay_periods:
                if pay_period.get('status') == "INITIAL":
                    state.company_worker_lookup_output_state.payperiodID = pay_period['payPeriodId']
                else:
                    continue

    except Exception as e:
        logger.error(f"Error in company_worker_lookup_get_payperiodID - {type(e).__name__} - {e}", exc_info=True)
    finally:
        return state

# LEGACY FUNCTION
async def company_worker_lookup_get_client_id_and_config(state: PayrollState) -> PayrollState:
    logger.info("company_worker_lookup_get_client_id_and_config called...")
    try:
        if state.company_worker_lookup_output_state.validate_sender_metadata.valid:
            mcp_service = get_mcp_service()
            email = state.input_state.SourceAddress
            logger.debug(f"Fetching client ID for email: {email}")
            response = await mcp_service.call_mcp_tool(server_name='paychex',
                                                        tool_name='paychex_lookup_account_by_email',
                                                        tool_input={"email": email})
            # Parse the response
            response = json.loads(response)
            response = ClientConfig.model_validate(response)

            # Add the response to the state
            state.company_worker_lookup_output_state.client_config = response
            logger.debug(f"company_worker_lookup_get_client_id_and_config got structured output: {response!r}")
        else:
            logger.warning(f"Sender metadata not valid; skipping client config fetch. Got {state.company_worker_lookup_output_state.validate_sender_metadata.errors}")

    except Exception as e:
        logger.error(f"Error in company_worker_lookup_get_client_id_and_config: {type(e).__name__} - {e}", exc_info=True)
    finally:
        return state


def company_worker_lookup_terminate(state: PayrollState) -> PayrollState:
    """Terminate account_lookup with reason when client ID is invalid."""
    logger.warning("company_worker_lookup_terminate called...")
    reason = state.company_worker_lookup_output_state.termination_reason
    logger.warning(f"Termination reason: {reason}")

    state.company_worker_lookup_output_state.termination_reason = reason
    state.company_worker_lookup_output_state.should_continue = False
    state.company_worker_lookup_output_state.termination_node = company_worker_lookup_terminate.__name__
    logger.debug(f"company_worker_lookup_terminate returning PayrollState: {state}")

    return state

# NEEDS TO BE UPDATED TO NOT USE RESULTS FROM  company_worker_lookup_get_client_id_and_config fucntion
def company_worker_lookup_router(state: PayrollState) -> PayrollState:
    logger.info("company_worker_lookup_router called...")

    company_data = state.company_worker_lookup_output_state
    account_id = company_data.client_config.account_id
    company_lookup = company_data.company_lookup
    llm_workers_match = company_data.llm_workers_match.workers
    matched_workers = list(company_data.workers_matched.values())

    failures = []

    if company_lookup.get("errors"):
        failures.append("Company lookup failed, company not in DB")

    if not company_data.payperiodID:
        failures.append("No pay period found for the company")

    if any([x.confidence<ROUTERS_CONFIG["llm_name_matching_confidence"] for x in llm_workers_match]):
        failures.append("LLM name matching confidence too low")

    if sum([worker['currentStatus']['statusType']=='ACTIVE' for worker in matched_workers])< \
                                                                    ROUTERS_CONFIG['min_num_of_active_employees']:
        failures.append("Not enough active employees")

    if len(company_data.employees_metadata) == 0:
        failures.append(f"No employees detected in the email, got {company_data.employees_metadata}")

    if type(account_id) == str:
        pass
    elif type(account_id) == list and len(account_id) != ROUTERS_CONFIG['number_of_account_ids']:
        failures.append(f"Expected {ROUTERS_CONFIG['number_of_account_ids']} account IDs, got {account_id}")
    else:
        failures.append(f"Account ID got a wrong type, expected str or list of str, got {account_id}")

    if company_data.client_config.lookup_status != ROUTERS_CONFIG["lookup_status"]:
        failures.append(f"Client config lookup failed, got {company_data.client_config.lookup_status} expected {ROUTERS_CONFIG['lookup_status']}")
        logger.warning(f"Error: {company_data.client_config.error}")
        logger.warning(f"Suggestion: {company_data.client_config.suggestion}")

    if company_data.client_config.status != ROUTERS_CONFIG ["account_status"]:
        failures.append(f"Client status is not {ROUTERS_CONFIG['account_status']}, got {company_data.client_config.status}")

    if not failures:
        logger.info("Client config valid and payroll is for one client")
        company_data.is_client_id_valid = True
        company_data.should_continue = True
    else:
        error_message = "; ".join(failures)
        logger.warning(f"company_worker_lookup_router failure: {error_message}")
        company_data.termination_reason = error_message

    return state


def company_worker_lookup_finishing_node(state: PayrollState) -> PayrollState:
    logger.info("company_worker_lookup_finishing_node called...")
    return state
