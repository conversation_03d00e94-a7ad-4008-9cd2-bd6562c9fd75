from typing import Tuple, List

from app.cli.logging_utils import setup_logger
from app.payroll_agent.config.config import settings
from app.payroll_agent.graph.states.classification import ClassificationOutputState
from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.models.classification import IntentTriage, L1Intents, RequestTypeTags, ComplexityTags
from app.payroll_agent.utils.funcs import load_routers_config
from app.payroll_agent.utils.prompt import load_prompt


# Set up the logger
logger = setup_logger('payroll_agent.nodes.account_lookup')


PROMPTS = load_prompt("classification")
ROUTERS_CONFIG = load_routers_config('routers_config')['classification']
TRIAGE_ROUTERS_CONFIG = ROUTERS_CONFIG["intent_triage"]
L1_ROUTERS_CONFIG = ROUTERS_CONFIG["l1_intent"]
RULES_CONFIG = load_routers_config('rules_config')['classification']
COMPLEXITY_RULES_CONFIG = RULES_CONFIG["complexity_rules"]


def classification_intent_triage(state: PayrollState) -> PayrollState:
    logger.info("intent_triage called...")

    try:
        llm = settings.LLM().with_structured_output(schema=IntentTriage, method="function_calling")

        prompt = PROMPTS["intent_triage_classification"]
        message = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": state.input_state.EmailContent}
            ]

        logger.debug(f"LLM prompt for l0 intent_triage: {message}")

        # Invoke the LLM with the prompt
        result = llm.invoke(message)
        logger.debug(f"intent_triage got structured output: {result}")
        state.classification_output_state = ClassificationOutputState(intent_triage_metadata=result)
        logger.debug(f"intent_triage returning OutputSate: {state}")

        return state

    except Exception as e:
        logger.error(f"Error in intent_triage. Error: {type(e).__name__} - {e}", exc_info=True)
        raise
        # Raise the exception to propagate it upwards


def classification_l1_intent(state: PayrollState) -> PayrollState:
    logger.info("l1_intent called...")
    try:
        llm = settings.LLM().with_structured_output(schema=L1Intents, method="function_calling")

        prompt = PROMPTS["l1_intent_classification"]
        message = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": state.input_state.EmailContent}
            ]

        logger.debug(f"LLM prompt for l1_intent: {message}")

        # Invoke the LLM with the prompt
        result = llm.invoke(message)
        logger.debug(f"l1_intent got structured output: {result}")

        state.classification_output_state.l1_intent_metadata = result
        logger.debug(f"l1_intent returning OutputSate: {state}")

        return state

    except Exception as e:
        logger.error(f"Error in l1_ intents. Error: {type(e).__name__} - {e}", exc_info=True)
        raise


def classification_intent_triage_router(state: PayrollState) -> PayrollState:
    logger.info("classification_intent_triage_router called...")

    triage_data = state.classification_output_state.intent_triage_metadata

    # Evaluate each condition from config
    failures = []

    for field, threshold in TRIAGE_ROUTERS_CONFIG.items():
        value = getattr(triage_data, field, None)
        if value is None:
            logger.warning(f"Field '{field}' not found in triage data.")
            failures.append(field)
        elif value < threshold:
            logger.debug(f"Triage check failed: {field} = {value} < {threshold}")
            failures.append(field)

    if not failures:
        logger.debug("classification_intent_triage_router: all conditions met, passes triage")
        state.classification_output_state.intent_triage_passes_flag = True
    else:
        logger.debug(f"classification_intent_triage_router: fails triage due to {failures}")
        state.classification_output_state.intent_triage_passes_flag = False
        state.classification_output_state.intent_triage_passes_fail_reasons = failures  # Optional: log which ones failed

    return state


def classification_l1_intent_router(state: PayrollState) -> PayrollState:
    logger.info("classification_l1_intent_router called...")

    l1_data = state.classification_output_state.l1_intent_metadata
    failures = []

    for field, threshold in L1_ROUTERS_CONFIG.items():
        value = getattr(l1_data, field, None)
        if value is None:
            logger.warning(f"Field '{field}' not found in L1 intent data.")
            failures.append(field)
        elif value < threshold:
            logger.debug(f"L1 intent check failed: {field} = {value} < {threshold}")
            failures.append(field)

    if not failures:
        logger.debug("classification_l1_intent_router: all L1 conditions met, in scope")
        state.classification_output_state.l1_intent_passes_flag = True
        state.classification_output_state.should_continue = True
    else:
        logger.debug(f"classification_l1_intent_router: fails L1 intent check due to {failures}")
        state.classification_output_state.l1_intent_passes_flag = False
        state.classification_output_state.should_continue = False
        state.classification_output_state.l1_intent_passes_fail_reasons = failures  # Optional

    return state


def classification_request_type(state: PayrollState) -> PayrollState:
    logger.info("classification_request_type called...")
    try:
        llm = settings.LLM().with_structured_output(schema=RequestTypeTags, method="function_calling")

        prompt = PROMPTS["request_type_classification"]
        message = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": state.input_state.EmailContent}
        ]

        logger.debug(f"LLM prompt for request_type: {message}")

        result = llm.invoke(message)
        logger.debug(f"Request type tagging result: {result}")

        state.classification_output_state.request_type_metadata = result
        logger.debug(f"classification_request_type returning OutputState: {state}")

        return state

    except Exception as e:
        logger.error(f"Error in classification_request_type. Error: {type(e).__name__} - {e}", exc_info=True)
        raise


def _tag_complexity_from_config(tags: RequestTypeTags, config: dict) -> Tuple[str, List[str]]:
    data = tags.dict()
    unknown_count = sum(1 for value in data.values() if value == "unknown")
    reasons = []

    # Check hard rules (any match)
    for rule in config.get("hard", {}).get("Any", []):
        if isinstance(rule, dict):
            for field, values in rule.items():
                if field == "UnknownCount":
                    if unknown_count >= int(values[0].lstrip(">=")):
                        reasons.append("Too many unknowns")
                        return "hard", reasons
                elif data.get(field) in values:
                    reasons.append(f"{field}={data.get(field)}")
                    return "hard", reasons

    # Check easy rules (all must match)
    easy_criteria = config.get("easy", {})
    unmatched = []
    for field, values in easy_criteria.items():
        if data.get(field) not in values:
            unmatched.append(field)

    if not unmatched:
        return "easy", ["All easy criteria matched"]

    # If not easy or hard, return medium + what triggered medium
    reasons = [f"{field}={data.get(field)} not in easy group" for field in unmatched]
    return "medium", reasons


def classification_complexity(state: PayrollState) -> PayrollState:
    logger.info("classification_complexity called...")

    try:
        request_tags = state.classification_output_state.request_type_metadata
        if request_tags is None:
            raise ValueError("Missing request_type_metadata in state. Cannot determine complexity.")

        # Apply rules
        complexity_tag, complexity_tag_reasons = _tag_complexity_from_config(request_tags, COMPLEXITY_RULES_CONFIG)
        logger.debug(f"Determined complexity: {complexity_tag}")

        # Package into ComplexityTag model
        complexity_metadata = ComplexityTags(
            ComplexityTag=complexity_tag,
            ComplexityTagReason=complexity_tag_reasons
        )
        # Store in output state
        state.classification_output_state.complexity_metadata = complexity_metadata
        logger.debug(f"classification_complexity returning OutputState: {state}")

        return state

    except Exception as e:
        logger.error(f"Error in classification_complexity: {type(e).__name__} - {e}", exc_info=True)
        raise


def classification_terminate(state: PayrollState) -> PayrollState:
    """Terminate account_lookup with reason when client ID is invalid."""
    logger.warning("classification_terminate called...")
    reason = f"Confidence low in being single task and easy to complete, hint: {state.classification_output_state}"
    logger.warning(f"Termination reason: {reason}")

    state.classification_output_state.termination_reason = reason
    state.classification_output_state.should_continue = False
    state.classification_output_state.termination_node = classification_terminate.__name__
    logger.debug(f"classification_terminate returning PayrollState: {state}")

    return state


def classification_finish_node(state: PayrollState) -> PayrollState:
    logger.info("classification_finishing_node called...")
    return state

