import re
import json

from datetime import datetime
from typing import List, Dict, Any

from langchain.agents import tool, initialize_agent, AgentType
from langgraph.prebuilt import create_react_agent

from app.cli.logging_utils import setup_logger
from app.payroll_agent.config.config import settings
from app.payroll_agent.utils.prompt import load_prompt
from app.payroll_agent.utils.funcs import load_routers_config

from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.models.payroll_processing import Payroll, PayrollEntries

from app.payroll_agent.utils.mcp import get_mcp_service


# Set up the logger
logger = setup_logger(__name__)

PROMPTS = load_prompt("payroll_processing")
ROUTERS_CONFIG = load_routers_config('routers_config')['payroll_processing']


@tool("extract_payroll_commands", description="Extract raw payroll commands from an email. Input is JSON with 'email_text'.")
def extract_payroll_commands_tool(email_and_ctx: str) -> str:
    """
    Input: A JSON blob with keys "email_text".
    Output: JSON string of parsed_commands.
    """
    ctx = json.loads(email_and_ctx)
    email = ctx["email_text"]
    llm = settings.LLM().with_structured_output(Payroll)
    prompt = PROMPTS["extract_payroll_commands"]

    message = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": email}
        ]
    resp = llm.invoke(message)

    return  json.dumps(resp.model_dump().get("commands", []))


@tool("filter_payroll_commands", description="Filter parsed payroll commands by employee list. Input is JSON with 'parsed_commands' and 'employee_list'.")
def filter_payroll_commands_tool(parsed_and_ctx: str) -> str:
    """
    Input: JSON blob with keys "parsed_commands" and "employee_list".
    Output: JSON string of filtered_commands.
    """
    ctx = json.loads(parsed_and_ctx)
    parsed = ctx["parsed_commands"]
    employees = set(ctx["employee_list"])
    filtered = [cmd for cmd in parsed if cmd.get('name', '') in employees]
    return json.dumps(filtered)


@tool("compute_missing_commands",description="Compute the missing amounts/hours/units vs existing metadata. Input is JSON with 'filtered_commands' and 'existing_metadata'." )
def compute_missing_commands_tool(filtered_and_ctx: str) -> str:
    """
    Input: JSON blob with keys "filtered_commands" and "existing_metadata".
    Output: JSON string of missing_commands.
    """
    ctx = json.loads(filtered_and_ctx)
    filtered = ctx["filtered_commands"]
    existing = ctx["existing_metadata"]
    missing: List[Dict] = []

    for cmd in filtered:
        cmd2 = cmd.copy()  # Make a copy to modify
        name = cmd.get('name', '')
        meta = existing.get(name, {})

        # Case A: straight amount
        if "payAmount" in cmd.get('command', {}):
            already = float(meta.get("payAmount", 0.0))
            needed = cmd['command']["payAmount"] - already
            if needed > 0:
                cmd2['command'] = {"payAmount": round(needed, 2)}
                missing.append(cmd2)
            continue

        # Case B: hours or units
        for unit_key in ("payHours", "payUnits"):
            if unit_key in cmd.get('command', {}):
                already = float(meta.get(unit_key, 0.0))
                needed = cmd['command'][unit_key] - already
                if needed > 0:
                    out: Dict[str, Any] = {unit_key: round(needed, 2)}
                    if "payRate" in cmd.get('command', {}) and cmd['command']["payRate"]:
                        out["payRate"] = float(cmd['command']["payRate"])
                    elif "payRate" in meta:
                        out["payRate"] = float(meta["payRate"])
                    if "payRateId" in cmd.get('command', {}) and cmd['command']["payRateId"]:
                        out["payRateId"] = cmd['command']["payRateId"]
                    elif "payRateId" in meta:
                        out["payRateId"] = meta["payRateId"]
                    cmd2['command'] = out
                    missing.append(cmd2)
                break

    return json.dumps(missing)


def create_payroll_agent(agent_number: int = 1):
    """
    Creates a payroll extraction agent with the specified agent number.
    This function is used to create agents for payroll extraction tasks.
    """
    logger.info(f"Creating payroll extraction agent {agent_number}")
    return initialize_agent(
        tools=[extract_payroll_commands_tool, filter_payroll_commands_tool, compute_missing_commands_tool],
        llm=settings.LLM(agent_number=agent_number),
        agent=AgentType.ZERO_SHOT_REACT_DESCRIPTION,
        verbose=True,
        handle_parsing_errors=True,
        return_intermediate_steps=True,
    )


def get_existing_metadata(state:PayrollState, employee_list: List[str]):
    logger.debug(f"Retrieving existing metadata for employees: {employee_list}")
    checks = state.payroll_processing_output_state.company_checks
    existing_metadata = dict()

    if len(checks) > 0:
        for employee in employee_list:
            worker_id = state.company_worker_lookup_output_state.workers_matched[employee].get('workerId','')
            if worker_id:
                # find the check for this worker
                check = next((c for c in checks if c.get('workerId') == worker_id), None)
                if check:
                    # add the check to the employee metadata
                    if check.get("earnings"):
                        existing_metadata[employee] = check["earnings"][0]

    return existing_metadata


async def payroll_processing_get_company_checks(state: PayrollState) -> PayrollState:
    logger.info("payroll_processing_get_company_checks called...")
    mcp_service = get_mcp_service()
    # Get the company checks from the MCP service
    company_checks_tool_name = "paychex_company_cheks"
    logger.debug(f"Retrieving tool {company_checks_tool_name} from server 'paychex'")
    company_checks_tool = await mcp_service.get_tool(server_name="paychex", tool_name=company_checks_tool_name)

    company_checks = await company_checks_tool.arun(
                        {
                            "companyID": "A1",
                            "payperiodID" : state.company_worker_lookup_output_state.payperiodID
                        }
                    )
    logger.debug(f"Retrieved company checks: {company_checks}")

    # Parse the company checks into the state
    state.payroll_processing_output_state.company_checks =  json.loads(company_checks).get('content', [])
    return state


def payroll_processing_agent_run(state: PayrollState) -> PayrollState:
    logger.info("payroll_processing_agent_run called...")

    # create the agent
    email_content = state.input_state.EmailContent
    employee_list = list(state.company_worker_lookup_output_state.workers_matched.keys())

    ## get the company checks
    existing_metadata = get_existing_metadata(state, employee_list)

    # prepare the input for the agent
    full_ctx = {
        "email_text": email_content,
        "employee_list": employee_list,
        "existing_metadata": existing_metadata
    }

    # create the agent
    agent = create_payroll_agent(agent_number=1)

    logger.debug(f"Running agent with input: {full_ctx}")
    input = json.dumps(full_ctx)
    result = agent.invoke(input)
    logger.debug(f"Raw agent output: {result}")

    # Parse the result
    parsed_commands = json.loads(result['intermediate_steps'][-1][1])
    state.payroll_processing_output_state.payroll_commands_agent_1 = parsed_commands
    return state


def payroll_processing_agent_2_run(state: PayrollState) -> Dict:
    logger.info("payroll_processing_agent_run called...")

    # create the agent
    email_content = state.input_state.EmailContent
    employee_list = list(state.company_worker_lookup_output_state.workers_matched.keys())

    ## get the company checks
    existing_metadata = get_existing_metadata(state, employee_list)

    # prepare the input for the agent
    full_ctx = {
        "email_text": email_content,
        "employee_list": employee_list,
        "existing_metadata": existing_metadata
    }

    # create the agent
    agent = create_payroll_agent(agent_number=2)

    logger.debug(f"Running agent with input: {full_ctx}")
    input = json.dumps(full_ctx)
    result = agent.invoke(input)
    logger.debug(f"Raw agent output: {result}")

    # Parse the result
    parsed_commands = json.loads(result['intermediate_steps'][-1][1])
    state.payroll_processing_output_state.payroll_commands_agent_2 = parsed_commands
    return {}


## LEGACY FUNCTION
def parse_ai_message(ai_msg: str) -> json:
    # Strip any ```json fences
    #    e.g. ```json\n{ ... }\n```
    json_str = re.sub(r"^```json\s*", "", ai_msg)
    json_str = re.sub(r"\s*```$", "", json_str)

    # 4) Parse it
    try:
        payload = json.loads(json_str)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse JSON from agent: {json_str} {type(e).__name__} - {e}", exc_info=True)
        raise

    # 5) Validate with Pydantic
    try:
        entries = PayrollEntries.model_validate(payload)
    except Exception as e:
        logger.error(f"Failed to validate payroll entries: {payload} {type(e).__name__} - {e}", exc_info=True)
        raise

    return entries

# LEGACY FUNCTION
async def payroll_processing_create_agent(account_config_template: dict, agent_number: int = 1):
    """
    Initializes and returns a payroll extraction React agent
    """
    logger.info("Starting payroll_processing_create_agent")
    try:
        # set llm
        logger.debug("Setting up LLM model")
        llm = settings.LLM(agent_number=agent_number)
        logger.debug(f"LLM initialized: {llm}")

        # set mcp client
        logger.debug("Creating MCPService client")
        mcp_service = get_mcp_service()
        logger.debug("MCPService client created")

        # tools
        past_email_tool_name = "paychex_lookup_past_emails_by_email"
        logger.debug(f"Retrieving tool {past_email_tool_name} from server 'paychex'")
        # get tools from mcp
        past_email_tool = await mcp_service.get_tool(
            server_name="paychex",
            tool_name=past_email_tool_name,
            )
        logger.debug(f"Retrieved tool: {past_email_tool}")

        # format prompt
        logger.debug(f"Formatting agent prompt using template 'agent_{agent_number}'")
        base_prompt = PROMPTS[f"agent_{agent_number}"]

        # Get current year for date processing
        current_year = datetime.now().year

        agent_prompt = base_prompt.format(
            past_email_tool_name=past_email_tool_name,
            account_config_template=account_config_template,
            current_year=current_year
        )
        logger.debug(f"Formatted agent prompt: {agent_prompt}")

        # create agent
        logger.debug(f"Creating React agent 'payroll_agent_{agent_number}'")
        payroll_agent = create_react_agent(
            model=llm,
            tools=[past_email_tool],
            prompt=agent_prompt,
            name=f"payroll_agent_{agent_number}",
        )
        logger.debug("Payroll extraction agent created successfully")
        return payroll_agent
    except Exception as e:
        logger.error(f"Failed to create payroll extraction agent: {type(e).__name__} - {e}", exc_info=True)
        raise e

# LEGACY FUNCTION
async def payroll_processing_run_agent(state: PayrollState):
    logger.info("Starting payroll_processing_run_agent_1")

    # 1) Crete the agent
    email_content = state.input_state.EmailContent
    config_template = state.company_worker_lookup_output_state.client_config.account_config_template
    agent = await payroll_processing_create_agent(account_config_template=config_template)

    # Call the agent
    raw = await agent.ainvoke({"messages": email_content})
    logger.debug(f"Raw agent_1 output: {raw}")

    # Extract the AIMessage content
    #    (raw is {'messages': [HumanMessage(...), AIMessage(content=...)]})
    ai_msg = raw["messages"][-1].content

    entries = parse_ai_message(ai_msg)

    #  Save into your state and return
    state.payroll_processing_output_state.extracted_payrolls_agent_1 = entries

    return state

# LEGACY FUNCTION
async def payroll_processing_run_agent_2(state: PayrollState) -> Dict:
    logger.info("Starting payroll_processing_run_agent_2")

    # 1) Crete the agent
    email_content = state.input_state.EmailContent
    config_template = state.company_worker_lookup_output_state.client_config.account_config_template
    agent = await payroll_processing_create_agent(account_config_template=config_template, agent_number=2)

    # Call the agent
    raw = await agent.ainvoke({"messages": email_content})
    logger.debug(f"Raw agent_2 output: {raw}")

    # Extract the AIMessage content
    #    (raw is {'messages': [HumanMessage(...), AIMessage(content=...)]})
    ai_msg = raw["messages"][-1].content

    entries = parse_ai_message(ai_msg)

    # Save into your state and return
    state.payroll_processing_output_state.extracted_payrolls_agent_2 = entries

    # The update to state is done, returning empty to avoid langgraph merging states
    return {}

# NEEDS TO BE UPDATED TO NOt USE RESULTS FROM LEGACY FUNCTIONS
def payroll_processing_router(state: PayrollState) -> PayrollState:
    logger.info("payroll_processing_router called...")

    payrolls_agent_1 = state.payroll_processing_output_state.payroll_commands_agent_1
    payrolls_agent_2 = state.payroll_processing_output_state.payroll_commands_agent_2

    extraction_1 = state.payroll_processing_output_state.extracted_payrolls_agent_1
    extraction_2 = state.payroll_processing_output_state.extracted_payrolls_agent_2

    payroll_entries_1 = extraction_1.payroll_entries
    payroll_entries_2 = extraction_2.payroll_entries

    failures = []

    if payrolls_agent_1 != payrolls_agent_2:
        error_message = f"Payroll commands mismatch, agent_1: {payrolls_agent_1}, agent_2: {payrolls_agent_2}"
        failures.append(error_message)
        logger.warning(error_message)

    if extraction_1.success == extraction_2.success == ROUTERS_CONFIG["extraction_success_flag"]:
        pass
    else:
        error_message = f"Payroll extraction failed, error: extraction_1 error: {extraction_1.error}, extraction_2 error: {extraction_2.error}"
        failures.append(error_message)
        logger.warning(error_message)

    if len(payroll_entries_1) != len(payroll_entries_2):
        error_message = f"Payroll extraction length mismatch, extraction_1 length: {len(payroll_entries_1)}, extraction_2 length: {len(payroll_entries_2)}"
        failures.append(error_message)
        logger.warning(error_message)
    else:
        # validate that both extractions have the same keys and values
        for i, employee_extraction in enumerate(payroll_entries_1):
            for key in employee_extraction:
                if employee_extraction[key] != payroll_entries_2[i].get(key, None):
                    error_message = f"Payroll extraction values mismatch for key '{key}', extraction_1 value: {employee_extraction[key]}, extraction_2 value: {payroll_entries_2[i][key]}"
                    failures.append(error_message)
                    logger.warning(error_message)

    if not failures:
        logger.info("Payroll extraction successful")
        state.payroll_processing_output_state.extracted_payrolls = extraction_1
        state.payroll_processing_output_state.should_continue = True
    else:
        error_message = "; ".join(failures)
        state.payroll_processing_output_state.extracted_payrolls.success = False
        logger.warning(f"payroll_extraction failure: {error_message}")
        state.payroll_processing_output_state.termination_reason = error_message

    return state


def payroll_processing_terminate(state: PayrollState) -> PayrollState:
    """Terminate intake with reason when client ID is invalid."""
    logger.warning("payroll_processing_terminate called...")
    reason = state.payroll_processing_output_state.termination_reason
    logger.warning(f"Termination reason: {reason}")

    state.payroll_processing_output_state.should_continue = False
    state.payroll_processing_output_state.termination_node = payroll_processing_terminate.__name__
    logger.debug(f"payroll_processing_terminate returning PayrollState: {state}")
    return state


def payroll_processing_finishing_node(state: PayrollState) -> PayrollState:
    logger.info("payroll_processing_finishing_node called...")
    return state
