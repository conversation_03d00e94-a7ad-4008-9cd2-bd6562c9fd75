import re
import json

from langgraph.prebuilt import create_react_agent

from app.cli.logging_utils import setup_logger
from app.payroll_agent.config.config import settings
from app.payroll_agent.utils.prompt import load_prompt
from app.payroll_agent.utils.funcs import load_routers_config

from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.models.validation import ValidatedPayrollEntries

from app.payroll_agent.utils.mcp import get_mcp_service


# Set up the logger
logger = setup_logger(__name__)

PROMPTS = load_prompt("validation")
ROUTERS_CONFIG = load_routers_config('routers_config')['validation']


async def validation_create_agent():
    """
    Initializes and returns a payroll extraction React agent
    """
    logger.info("Starting validation_create_agent")
    try:
        # set llm
        logger.debug("Setting up LLM model")
        llm = settings.LLM()
        logger.debug(f"LLM initialized: {llm}")

        # set mcp client
        logger.debug("Creating MCPService client")
        mcp_service = get_mcp_service()
        logger.debug("MCPService client created")

        # tools
        past_email_tool_name = "paychex_lookup_past_emails_by_email"
        logger.debug(f"Retrieving tool {past_email_tool_name} from server 'paychex'")
        # get tools from mcp
        past_email_tool = await mcp_service.get_tool(
            server_name="paychex",
            tool_name=past_email_tool_name,
            )
        logger.debug(f"Retrieved tool {past_email_tool}")

        # format prompt
        logger.debug("Formatting agent prompt using template")
        base_prompt = PROMPTS["validation_agent"]
        val_agent_prompt = base_prompt.format(
                                            past_email_tool_name=past_email_tool_name
                                            )
        logger.debug(f"Formatted agent prompt: {val_agent_prompt}")

        # create agent
        logger.debug("Creating React agent 'val_agent'")
        val_agent = create_react_agent(
            model=llm,
            tools=[past_email_tool],
            prompt=val_agent_prompt,
            name="val_agent",
        )
        logger.debug(f"Validation agent created successfully: {val_agent}", )
        return val_agent
    except Exception as e:
        logger.error(f"Failed to create validation agent: {type(e).__name__} - {e}", exc_info=True)
        raise e

# NEEDS TO BE UPDATED TO USE RESULTS FROM THE NEW AGENT VERSIONS
async def validation_run_agent(state: PayrollState) -> PayrollState:
    logger.info("Starting validation_run_agent")

    # 1) Call the agent
    email_content = state.input_state.EmailContent
    agent = await validation_create_agent()

    message = json.dumps({
                "email_text": email_content,
                "payroll_entries": state.payroll_processing_output_state.extracted_payrolls.payroll_entries
              })

    logger.debug(f"Sending message: {message}")
    raw = await agent.ainvoke({"messages": message})

    logger.debug(f"Raw agent output: {raw}")

    # 2) Extract the AIMessage content
    #    (raw is {'messages': [HumanMessage(...), AIMessage(content=...)]})
    ai_msg = raw["messages"][-1].content

    # 3) Strip any ```json fences
    #    e.g. ```json\n{ ... }\n```
    json_str = re.sub(r"^```json\s*", "", ai_msg)
    json_str = re.sub(r"\s*```$", "", json_str)

    # 4) Parse it
    try:
        payload = json.loads(json_str)
        logger.debug(f"Sending message: {payload}")
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse JSON from agent: {json_str} {type(e).__name__} - {e}", exc_info=True)
        raise

    # 5) Validate with Pydantic
    validated_entries = ValidatedPayrollEntries.model_validate(payload)

    # 6) Save into your state and return
    state.validation_output_state.validated_payroll_entries = validated_entries

    return state


def validation_router(state: PayrollState) -> PayrollState:
    logger.info("validation_router called, passing through state")

    # checks
    conditions = [
        state.validation_output_state.validated_payroll_entries.success,
        ]

    #check all conditions are met
    if all(conditions):
        state.validation_output_state.should_continue = True
        logger.info("Payrolls validated successfully")
    else:
        error_message = f"Payroll validation failed, error: {state.validation_output_state.validated_payroll_entries.error} "
        logger.warning(error_message)
        state.validation_output_state.termination_reason = error_message
    return state


def validation_terminate(state: PayrollState) -> PayrollState:
    """Terminate intake with reason when client ID is invalid."""
    logger.warning("validation_terminate called...")
    reason = state.validation_output_state.termination_reason
    logger.warning(f"Termination reason: {reason}")

    state.validation_output_state.should_continue = False
    state.validation_output_state.termination_node = validation_terminate.__name__
    logger.debug(f"validation_terminate returning PayrollState: {state}")
    return state


def validation_finishing_node(state: PayrollState) -> PayrollState:
    logger.info("validation_terminate called...")
    return state
