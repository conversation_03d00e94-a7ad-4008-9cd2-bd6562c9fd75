from app.cli.logging_utils import setup_logger
from app.payroll_agent.utils.funcs import load_routers_config

from app.payroll_agent.graph.states.common import PayrollState
from app.payroll_agent.graph.states.execution import ExecutionOutputState

# Set up the logger
logger = setup_logger(__name__)

ROUTERS_CONFIG = load_routers_config('routers_config')['execution']

## NEEDS TO BE UPDATED TO USE RESULTS FROM THE NEW VERSION OF THE AGENT
def execution_create_payrolls(state: PayrollState) -> PayrollState:
    """Create payrolls from the extracted payroll entries."""
    logger.info("execution_create_payrolls called...")
    # create payrolls
    payrolls = state.payroll_processing_output_state.extracted_payrolls.payroll_entries
    ### placeholder for actual payroll creation logic
    ########################
    state.execution_output_state = ExecutionOutputState(completed_payrolls=payrolls)
    logger.debug(f"Created payrolls: {state.execution_output_state.completed_payrolls}")
    return state


def execution_success_payroll_router(state: PayrollState) -> PayrollState:
    logger.info("execution_success_payroll_router called...")

    #check all conditions are met
    payrolls = state.execution_output_state.completed_payrolls
    if payrolls and len(payrolls) >= ROUTERS_CONFIG['min_payrolls']:
        state.execution_output_state.completed_payrolls_flag = True
        state.execution_output_state.should_continue = True
        logger.info("Payroll extraction successful")
    else:
        state.execution_output_state.completed_payrolls_flag = False
        error_message = f"Payrolls could not be create according to format template, payrolls: {payrolls} "
        logger.warning(error_message)
        state.execution_output_state.termination_reason = error_message
    return state


def execution_terminate(state: PayrollState) -> PayrollState:
    """Terminate intake with reason when client ID is invalid."""
    logger.warning("execution_terminate called...")
    reason = state.execution_output_state.termination_reason
    logger.warning(f"Termination reason: {reason}")

    state.execution_output_state.should_continue = False
    state.execution_output_state.termination_node = execution_terminate.__name__
    logger.debug(f"execution_terminate returning PayrollState: {state}")
    return state


def execution_finishing_node(state: PayrollState) -> PayrollState:
    logger.info("execution_finishing_node called...")
    return state
