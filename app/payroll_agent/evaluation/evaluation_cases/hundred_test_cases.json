[{"companyID": "A1", "comment": {"uid": "TEST_01", "id": "TEST_01", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC001"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON> and <PERSON><PERSON>,\nI wanted to provide an update on the preparations for the upcoming Wine & Jazz Weekend at the hotel.\n\nThe event space has been confirmed for setup on Friday morning, and the AV team will be arriving by 10:00 AM to handle the equipment. The catering team has finalized the menu and will begin food prep on Thursday evening.\n\nPlease make sure all signage is printed and placed by Friday afternoon. Guest gift bags should be delivered to each suite by 3:00 PM on Friday.\n\nLet me know if you need any additional support from the front desk or housekeeping team over the weekend.\n\nThank you,\nCal<PERSON> Vaughn\nLighthouse Hotel Group"}, "subject": {"text": "Planning Lighthouse Hotel Group"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_02", "id": "TEST_02", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC002"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON> and <PERSON><PERSON>,\nJust a quick update on the product launch timeline for the Nexus CRM platform.\n\nThe beta release is scheduled for June 12th, and the internal QA team has already flagged a few minor UI issues, which are being addressed in the next patch. Please ensure the marketing assets are finalized and uploaded to the campaign portal by EOD Wednesday.\n\nWe'll hold a final go-to-market alignment call with Sales, Customer Success, and Product on Friday at 2 PM EST. Zoom link to follow.\n\nAlso, don’t forget to review the launch checklist on Confluence and mark your sections as complete.\n\nThanks for the hard work everyone!\n\nBest,\n<PERSON>\nLaunch Manager\nArdent Cloud Systems"}, "subject": {"text": "Team Update - Nexus CRM Launch Timeline"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_03", "id": "TEST_03", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC003"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON> and <PERSON>,\n\nJust a heads-up that routine maintenance for the building HVAC systems will begin on Tuesday morning and continue through Thursday. Please inform any department leads that airflow may be limited during those windows.\n\nAlso, the lobby lighting installation is now confirmed for Friday between 7 AM and 11 AM. Ensure security has staff on-site to assist with vendor access.\n\nLet me know if any rescheduling is required.\n\nBest,\nTomas Redding\nFacilities Coordinator\nBayline Properties"}, "subject": {"text": "Scheduled Maintenance Notice - Bayline Tower"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_04", "id": "TEST_04", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC004"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON> and <PERSON>,\n\nJust a quick note to confirm that this week’s payroll has already been submitted on Monday morning by the Finance team. You do not need to take any further action at this time.\n\nWe’re currently reviewing the new overtime policy changes that go into effect next pay cycle. I’ll send an update early next week with any adjustments that might impact future processing.\n\nThanks for staying on top of things as always.\n\nBest,\n<PERSON>\nHR Operations\nTidepoint Manufacturing Inc."}, "subject": {"text": "Payroll Update – No Action Needed This Week"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_05", "id": "TEST_05", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC005"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON> and <PERSON>,\n\nJust confirming that next week’s payroll will follow the holiday schedule, with processing moved up to Wednesday, due to the public holiday on Friday.\n\nThere’s no need to submit anything additional right now — this is just a heads-up so you can plan around the adjusted timeline. We’ll still use the standard cutoff for hours worked through Sunday night.\n\nLet me know if any teams have concerns about hitting the earlier deadline.\n\nThanks,\n<PERSON> Grant\nFinance Team\nHolloway Distribution Corp."}, "subject": {"text": "FYI: Adjusted Payroll Schedule for Holiday Week"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_06", "id": "TEST_06", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC006"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi Team,\n\nJust a heads-up that the weekly server backup report is ready for review. The IT Ops team completed the full backup early this morning, and no errors were reported. Please verify the integrity logs and confirm that the offsite replication succeeded as expected.\n\nLet me know if you encounter any discrepancies or need further details.\n\nThanks,\n<PERSON>\nIT Operations\nAcme Corp."}, "subject": {"text": "Weekly Server Backup Report"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_07", "id": "TEST_07", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC007"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi All,\n\nFriendly reminder that the quarterly compliance training needs to be completed by end of day next Wednesday. The training modules are available in the Learning Portal under \"Q2 Compliance\". It should take approximately 45 minutes to finish.\n\nPlease ensure you complete the acknowledgement form once done. Failure to comply may result in access restrictions.\n\nBest,\nMegan Lee\nHR Business Partner\nAcme Corp."}, "subject": {"text": "Reminder: Q2 Compliance Training Due"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_08", "id": "TEST_08", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC008"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello Marketing Team,\n\nThe client satisfaction survey results from May have been compiled. Overall satisfaction is at 92%, with the highest-rated areas being product quality and support responsiveness. I've attached the detailed breakdown by region and department.\n\nPlease review the findings and share any action items for improvement by Friday.\n\nRegards,\nDana <PERSON>\nCustomer Experience Analyst\nAcme Corp."}, "subject": {"text": "May Client Satisfaction Survey Results"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_09", "id": "TEST_09", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC009"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi Social Media Team,\n\nThe content calendar for June has been finalized. We have 12 posts scheduled, including product highlights, customer testimonials, and behind-the-scenes videos. All assets are uploaded to the shared drive under \"June Social Media Calendar\".\n\nPlease confirm that you have access and let me know if any adjustments are needed before the 1st of the month.\n\nThanks,\n<PERSON>\nDigital Marketing Manager\nAcme Corp."}, "subject": {"text": "June Social Media Content Calendar"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_10", "id": "TEST_10", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC010"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi Procurement,\n\nWe need to reorder office supplies ahead of the summer season. Current inventory shows low stock on printer paper, toner cartridges, and notepads. Please process the purchase order by the end of the week to avoid any disruptions.\n\nLet me know if you need updated vendor quotes or approval workflows.\n\nCheers,\nEmily Soto\nOffice Manager\nAcme Corp."}, "subject": {"text": "Office Supply Reorder Request"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_011", "id": "TEST_011", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC011"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON> and <PERSON><PERSON>,\n\nPlease process the following payroll for tomorrow or Thursday.\nPlease confirm receipt of this email.\n\n<PERSON><PERSON><PERSON><PERSON>'<PERSON> and <PERSON>'s checks will be delivered to the business address:\nHotel Inn, 2067 Highway 77, Carville, NY 12222\n\nMukul Alda:\nDirect Deposit\n\nPay Rate:\nSharukh A Ford: $15.00/hour (Front Desk/Administration)\n<PERSON> Chevy: $15.00/hour (Housekeeping)\n<PERSON><PERSON><PERSON>: $15.00/hour (Front Desk/Administration)\n\nTimecards:\nFor the week of 11/04/24 - 11/10/24\n- <PERSON><PERSON><PERSON><PERSON>: 0.00 hours\n- <PERSON>: 9.5 hours\n- <PERSON><PERSON><PERSON>: 30 hours\n\nFor the week of 11/11/24 - 11/17/24\n- Sharukh: 0.00 hours\n- <PERSON>: 9.5 hours\n- <PERSON><PERSON><PERSON>: 30 hours\n\nAny questions, please let me know.\n\nThank you,\nMash Ferrari\nBeach Hotel LLC"}, "subject": {"text": "Payroll Submission - HighBeam Construction Co."}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_012", "id": "TEST_012", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC012"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON> and <PERSON><PERSON>,\n\nPlease process this week's payroll with the updated time breakdown and rate structures below. Note that weekend and overtime hours are billed differently, as per our agreement.\n\nEmployee Breakdown:\n\nLucia Tran:\nBase Rate: $18.00/hour\n- Regular Hours: 32\n- Overtime Hours (1.5x): 10\n- Weekend Hours (2x): 8\n\nDarnell Price:\nBase Rate: $20.00/hour\n- Regular Hours: 40\n- Overtime Hours (1.5x): 5\n- Weekend Hours (2x): 6\n\nAmira Solis:\nBase Rate: $17.50/hour\n- Regular Hours: 36\n- Overtime Hours (1.5x): 4.5\n- Weekend Hours (2x): 0\n\nNote:\nOvertime applies after 40 hours/week.\nWeekend hours are calculated separately and paid at 2x the base rate regardless of total weekly hours.\n\nPlease let me know if anything is unclear or if additional verification is required.\n\nThanks,\n<PERSON> Rivera\nPayroll Coordinator\n<PERSON>"}, "subject": {"text": "Payroll Submission - Falcon Freight Services"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_013", "id": "TEST_013", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC013"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON> and <PERSON>,\n\nPlease process payroll for the clinical team based on the breakdown below. Night shift hours are paid at a higher rate, and holiday shifts receive double pay as agreed.\n\nEmployee Breakdown:\n\n<PERSON>:\nBase Rate: $28.00/hour\n- Regular Hours: 38\n- Night Shift Hours (1.25x): 6\n- Holiday Hours (2x): 4\n\nElias Monroe:\nBase Rate: $30.00/hour\n- Regular Hours: 40\n- Overtime Hours (1.5x): 3\n- Holiday Hours (2x): 8\n\n<PERSON>:\nBase Rate: $26.50/hour\n- Regular Hours: 35\n- Night Shift Hours (1.25x): 10\n- Holiday Hours (2x): 0\n\nNote:\nNight shifts are any hours worked between 10:00 PM and 6:00 AM.\nHoliday hours are billed at 2x the base rate regardless of total weekly hours.\n\nBest,\nAvery Cardenas\nStaffing & Payroll Admin\nEvergreen Care Center"}, "subject": {"text": "Payroll Submission - Evergreen Care Center"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_014", "id": "TEST_014", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC014"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON> and <PERSON>,\n\nPlease go ahead and process this week's payroll using the same payment details as last week. There have been no changes in hours, rates, or employee status for this pay period.\n\nIf any discrepancies are noticed or if you require re-confirmation from our side, feel free to reach out before finalizing.\n\nThank you,\n\n<PERSON>\nFinance Coordinator\nBlueHawk Security Solutions"}, "subject": {"text": "Payroll Processing - Week Ending 05/24/2025"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_015", "id": "TEST_015", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC015"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON> and <PERSON>,\n\nPlease find this week’s payroll instructions below. A few team members have updated hour breakdowns that require special calculations. For all others, please use the same payment details as submitted in last week's email.\n\nUpdated Payroll Entries:\n\n<PERSON>:\nBase Rate: $24.00/hour\n- Regular Hours: 35\n- Overtime Hours (1.5x): 6\n- Sunday Hours (2x): 4\n\nMira Dalton:\nBase Rate: $22.50/hour\n- Regular Hours: 38\n- Weekend Hours (2x): 5\n\n<PERSON>:\nBase Rate: $26.00/hour\n- Regular Hours: 40\n- Project-Based Bonus: $150 (add flat bonus)\n\nNo Changes – Use Same as Last Submission:\n- <PERSON>\n- <PERSON>\n- <PERSON><PERSON>\n- <PERSON>\n\nLet me know once this has been queued or if you require any clarification.\n\nBest,\n\n<PERSON>\nHR & Payroll Lead\nBrightSpan Engineering"}, "subject": {"text": "Payroll Submission - Week of 05/19"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_016", "id": "TEST_016", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC016"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON> and <PERSON>,\n\nPlease process this week’s payroll with the following breakdown. Note that any hours over 37.5 are overtime (1.5×) and Saturday hours are at double rate (2×).\n\nEmployee Details:\n\n<PERSON>:\nBase Rate: $19.50/hour\n– Regular Hours: 37.5\n– Overtime Hours (1.5×): 4\n– Saturday Hours (2×): 3\n\n<PERSON>:\nBase Rate: $21.00/hour\n– Regular Hours: 40\n– Overtime Hours (1.5×): 2\n– Saturday Hours (2×): 5\n\n<PERSON>:\nBase Rate: $18.75/hour\n– Regular Hours: 36\n– Overtime Hours (1.5×): 5\n– Saturday Hours (2×): 0\n\nPlease confirm once queued or reach out for any discrepancies.\n\nThanks,\n<PERSON>\nPayroll Specialist\nAcme Corp."}, "subject": {"text": "Weekly Payroll Submission – Acme Corp."}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_017", "id": "TEST_017", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC017"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON> and <PERSON>,\n\nPlease run payroll for the manufacturing crew. Holiday shifts (Dec 24–Dec 26) are at 2×, and night shifts (10 PM–6 AM) at 1.25×.\n\nEmployee Breakdown:\n\n<PERSON>:\nBase Rate: $23.00/hour\n– Regular Hours: 38\n– Night Shift (1.25×): 8\n– Holiday (2×): 6\n\nLouis Park:\nBase Rate: $22.50/hour\n– Regular Hours: 40\n– Night Shift (1.25×): 4\n– Holiday (2×): 0\n\nPlease apply direct deposit for both and confirm receipt.\n\n<PERSON>,\n<PERSON>\nHR & Payroll Coordinator\nAcme Manufacturing"}, "subject": {"text": "Payroll Submission – Holiday & Night Shift Premiums"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_018", "id": "TEST_018", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC018"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello <PERSON><PERSON> and <PERSON>,\n\nProcess the following part-time staff payroll. All employees are paid flat 2× rate for Sunday hours.\n\nPart-Time Staff:\n\n<PERSON>:\nFlat Rate: $16.00/hour\n– Weekdays: 25 hours\n– Sunday (2×): 5 hours\n\nBrandon Hall:\nFlat Rate: $17.25/hour\n– Weekdays: 30 hours\n– Sunday (2×): 0 hours\n\n<PERSON>:\nFlat Rate: $15.50/hour\n– Weekdays: 20 hours\n– Sunday (2×): 8 hours\n\nLet me know if checks or DD confirmations are needed.\n\nThanks,\n<PERSON>\nStaffing & Payroll\nAcme Retail"}, "subject": {"text": "Part-Time Staff Payroll – Week of 05/26"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_019", "id": "TEST_019", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC019"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON> and <PERSON>,\n\nSubmit payroll for the consulting team. Include flat $200/week retainer plus hourly overages.\n\nConsultants:\n\n<PERSON>:\nWeekly Retainer: $200\n– Overage Hours: 12 @ $60/hour\n\n<PERSON><PERSON>:\nWeekly Retainer: $200\n– Overage Hours: 8 @ $60/hour\n\n<PERSON>:\nWeekly Retainer: $200\n– Overage Hours: 0 @ $60/hour\n\nTotal pay = retainer + (overages × rate). Please confirm totals.\n\n<PERSON><PERSON>,\n<PERSON>\nFinance Lead\nAcme Consulting"}, "subject": {"text": "Consulting Team Payroll – Retainer & Overage"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_020", "id": "TEST_020", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC020"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON> and <PERSON>,\n\nProcess payroll for the sales team. Commission bonus included below.\n\nSales Team:\n\n<PERSON>:\nBase Rate: $22.00/hour – 40 hours\nCommission Bonus: $350\n\nDerek Mills:\nBase Rate: $22.00/hour – 38 hours\nCommission Bonus: $500\n\nSara Kim:\nBase Rate: $22.00/hour – 40 hours\nCommission Bonus: $0\n\nAdd bonuses as flat amounts. Confirm direct deposit instructions remain the same.\n\nThanks,\nEvan <PERSON>\nSales Operations\nAcme Sales Group"}, "subject": {"text": "Sales Team Payroll – Commission Bonuses"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_021", "id": "TEST_021", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC021"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello <PERSON> and <PERSON>,\n\nPlease finalize this bi-weekly payroll. PTO hours and differential rates below.\n\nStaff Details:\n\n<PERSON>:\nBase Rate: $20.00/hour\n– Worked: 75 hours\n– PTO Used: 8 hours @ base rate\n\n<PERSON>:\nBase Rate: $19.00/hour\n– Worked: 70 hours\n– Weekend Differential (1.5×): 6 hours\n\nMark <PERSON>:\nBase Rate: $21.00/hour\n– Worked: 80 hours\n– PTO Used: 0\n\nApply PTO at base rate; differential for weekend hours only.\n\n<PERSON>,\n<PERSON>\nPayroll Administrator\nA<PERSON>me Biotech"}, "subject": {"text": "Bi-Weekly Payroll – PTO & Differential Rates"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_022", "id": "TEST_022", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC022"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON> and <PERSON>,\n\nProcess this period’s payroll. Include holiday bonus and per-diem allowances.\n\nTeam Breakdown:\n\n<PERSON>:\nBase Rate: $24.50/hour – 42 hours\nHoliday Bonus: $100\nPer-Diem: $50\n\n<PERSON> Schultz:\nBase Rate: $23.75/hour – 40 hours\nHoliday Bonus: $0\nPer-Diem: $75\n\nNina <PERSON>:\nBase Rate: $25.00/hour – 38 hours\nHoliday Bonus: $100\nPer-Diem: $0\n\nAdd bonuses and per-diem as flat amounts.\n\nThanks,\n<PERSON>\nAccounting Manager\nAcme Events"}, "subject": {"text": "Payroll Submission – Bonuses & Allowances"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_023", "id": "TEST_023", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC023"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON> and <PERSON>,\n\nPlease run payroll for our remote team. They have a fixed monthly stipend plus hourly extras.\n\nRemote Team:\n\n<PERSON>:\nMonthly Stipend: $3,000\nExtra Hours: 10 @ $45/hour\n\n<PERSON>:\nMonthly Stipend: $3,000\nExtra Hours: 5 @ $45/hour\n\nKaren <PERSON>:\nMonthly Stipend: $3,000\nExtra Hours: 0\n\nApply stipend plus extras. Confirm tax withholding remains unchanged.\n\n<PERSON><PERSON>,\n<PERSON>\nGlobal Payroll Lead\nAcme International"}, "subject": {"text": "Remote Team Payroll – Stipend & Extras"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_024", "id": "TEST_024", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC024"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON> and <PERSON><PERSON><PERSON>,\n\nProcess payroll for warehouse staff. Shift differential and safety bonus included.\n\nWarehouse Staff:\n\n<PERSON>:\nBase Rate: $18.00/hour – 45 hours\nNight Diff (1.2×): 10 hours\nSafety Bonus: $75\n\n<PERSON> Ortiz:\nBase Rate: $18.00/hour – 40 hours\nNight Diff (1.2×): 0\nSafety Bonus: $75\n\nLogan Burke:\nBase Rate: $18.00/hour – 42 hours\nNight Diff (1.2×): 8 hours\nSafety Bonus: $0\n\nApply differential and add safety bonus flat. Confirm once done.\n\nThank you,\n<PERSON>\nOperations Manager\nAcme Logistics"}, "subject": {"text": "Warehouse Staff Payroll – Differentials & Bonuses"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_025", "id": "TEST_025", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC025"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON> and <PERSON>,\n\nPlease execute this week’s payroll. Include project completion bonus for two employees.\n\nEmployees:\n\n<PERSON>:\nBase Rate: $29.00/hour – 40 hours\nProject Bonus: $500\n\nMegan Walsh:\nBase Rate: $29.00/hour – 38 hours\nProject Bonus: $0\n\nEthan Park:\nBase Rate: $29.00/hour – 42 hours\nProject Bonus: $300\n\nBonuses are flat amounts. Direct deposit as usual.\n\nBest,\nDerek <PERSON>\nProject Finance\nAcme Engineering"}, "subject": {"text": "Payroll – Project Bonuses Included"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_026", "id": "TEST_026", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC026"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON> and <PERSON>,\n\nProcess the following mixed-rate payroll. Day rate and hourly rate apply as noted.\n\nMixed-Rate Team:\n\nGeorgia King:\nDay Rate: $200/day – 3 days\nHourly Rate: $25/hour – 10 hours\n\n<PERSON> <PERSON>:\nDay Rate: $200/day – 4 days\nHourly Rate: $25/hour – 5 hours\n\nIvy Chan:\nDay Rate: $200/day – 2 days\nHourly Rate: $25/hour – 15 hours\n\nCalculate day rate separately, add hourly pay. Confirm totals.\n\n<PERSON><PERSON>,\n<PERSON>\nFinance Analyst\nAcme Productions"}, "subject": {"text": "Payroll – Mixed Day & Hourly Rates"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_027", "id": "TEST_027", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC027"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON> and <PERSON>,\n\nPlease finalize payroll for the IT support team. Include on-call stipends and overtime.\n\nIT Support:\n\n<PERSON>:\nBase Rate: $27.00/hour – 35 hours\nOn-Call Stipend: $150\nOvertime (1.5×): 5 hours\n\nHannah Li:\nBase Rate: $27.00/hour – 40 hours\nOn-Call Stipend: $150\nOvertime (1.5×): 0\n\nMonroe Tate:\nBase Rate: $27.00/hour – 38 hours\nOn-Call Stipend: $0\nOvertime (1.5×): 4 hours\n\nApply stipends and overtime. Confirm direct deposit batch.\n\nThanks,\n<PERSON>\nIT Finance Manager\nAcme Tech"}, "subject": {"text": "IT Support Payroll – Stipends & Overtime"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_028", "id": "TEST_028", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC028"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON> and <PERSON>,\n\nProcess payroll for the marketing interns. They’re paid a flat weekly stipend plus $12/hour for extra hours.\n\nInterns:\n\n<PERSON>:\nWeekly Stipend: $400\nExtra Hours: 6 @ $12/hour\n\n<PERSON><PERSON> <PERSON>:\nWeekly Stipend: $400\nExtra Hours: 10 @ $12/hour\n\n<PERSON> <PERSON>:\nWeekly Stipend: $400\nExtra Hours: 0\n\nAdd stipend and extra hours. Let me know once processed.\n\nBest,\n<PERSON>\nMarketing Coordinator\nAcme Media"}, "subject": {"text": "Intern Payroll – Stipend & Extras"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_029", "id": "TEST_029", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC029"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON> and <PERSON>,\n\nPlease run the bi-weekly payroll. Include shift premiums and uniform allowance.\n\nTeam Details:\n\n<PERSON>:\nBase Rate: $22.50/hour – 80 hours\nEvening Diff (1.1×): 12 hours\nUniform Allowance: $50\n\nMarcus Holt:\nBase Rate: $22.50/hour – 75 hours\nEvening Diff (1.1×): 0\nUniform Allowance: $50\n\nDana Fields:\nBase Rate: $22.50/hour – 82 hours\nEvening Diff (1.1×): 8 hours\nUniform Allowance: $0\n\nApply premiums and allowance. Confirm totals and direct deposit.\n\nThanks,\n<PERSON><PERSON> Park\nHR & Payroll\nAcme Services"}, "subject": {"text": "Bi-Weekly Payroll – Premiums & Allowance"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_030", "id": "TEST_030", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC030"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON> and <PERSON>,\n\nProcess this period’s payroll including performance bonuses and holiday pay.\n\nEmployees:\n\n<PERSON>:\nBase Rate: $28.00/hour – 40 hours\nPerformance Bonus: $250\nHoliday Pay (2×): 8 hours\n\n<PERSON>:\nBase Rate: $28.00/hour – 35 hours\nPerformance Bonus: $0\nHoliday Pay (2×): 4 hours\n\nLeo Grant:\nBase Rate: $28.00/hour – 42 hours\nPerformance Bonus: $300\nHoliday Pay (2×): 0\n\nBonuses and holiday pay are flat additions. Please confirm processing.\n\n<PERSON><PERSON>,\n<PERSON>\nCompensation Specialist\nAcme Enterprises"}, "subject": {"text": "Payroll – Bonuses & Holiday Pay"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_031", "id": "TEST_031", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC031"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON><PERSON> and <PERSON>,\nPlease process this payroll. It can be processed for and paid tomorrow or Thursday.\n<PERSON><PERSON><PERSON> is DD as always.\n<PERSON>'s checks get delivered to the business address.\n<PERSON> is back to working and she is set up for Direct Deposit.\nPlease confirm receipt of this email.\n\nPay rates:\n<PERSON><PERSON><PERSON>: $15.00/hour (Front Desk/Administration)\n<PERSON>: $16.00/hour (Housekeeping)\n<PERSON>: $15.00/hour (Housekeeping)\nBintou Pear: $15.00/hour (Housekeeping)\n<PERSON>: $15/hour (Maintenance)\n\nTimecards:\nFor the week of 11/04/24 - 11/10/24\n<PERSON><PERSON><PERSON> - 16.00 hours\nKimberly - 7.75 hours\nSamatha - 0.00 hours\nBintou - 0.00 hours\nHarold - 7.75 hours\n\nFor the week of 11/11/24 - 11/17/24\nAn<PERSON>li - 16.00 hours\nKimberly - 11.25 hours\nSamantha - 0.00 hours\nBintou - 0.00 hours\n<PERSON> - 8.00 hours\n\nThank you,\nMash Fruit\nFruitcompany LLC\nSuper 8 Fruitville, NY"}, "subject": {"text": "Payroll Submission - Fruitcompany LLC"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_032", "id": "TEST_032", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC032"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON>,\nHere is the Payroll for 10-21-24 to 11-17-24 Paydate 11-26-24\n<PERSON>\n123.23 Regular hours\n0 overtime hours\n0 bonus hours\n0 vacation hours\n0 holiday hours\n<PERSON><PERSON>\n119.05 Regular hours\n0 overtime hours\n0 bonus hours\n0 vacation hours\n0 holiday hours\n<PERSON> (<PERSON><PERSON>) Alpha\n117.32 regular hours\n0 overtime hours\n0 bonus hours\n0 Vacation Hours\n0 Holiday hours\n<PERSON>\n73.15 regular hours\n0 overtime hours\n0 bonus hours\n0 Vacation Hours\n0 Holiday hours\nJordan Jett<PERSON>\n116 regular hours\n0 overtime hours\n0 Vacation Hours\n0 Holiday hours\n<PERSON>\n158.58 regular hours\n7.07 overtime hours\n0 bonus hours\n0 Vacation hours\n0 Holiday hours\nJim <PERSON>\nRegular Salary $9000\n--\nThanks,\n<PERSON>\n555-230-4444\nNu Precision Materials"}, "subject": {"text": "Payroll Submission - 10/21 to 11/17 Pay Period"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_033", "id": "TEST_033", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC033"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Good Morning! Here is payroll for Friday 11/22, please let me know if you need anything else to process this.\n<PERSON> - $78.5 regular\n\nThank you.\n<PERSON>\nAB Sign & Print"}, "subject": {"text": "Payroll for 11/22 - AB Sign & Print"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_034", "id": "TEST_034", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC034"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON> Towing Payroll Week Ending 11/17/24\n\nRobert    reg. hours 40.00, overtime hours 5.50\nWilliam    reg. hours 40.00, overtime hours 2.50, $134.50\n<PERSON> C    reg. hours 40.00, overtime hours 4.00\n<PERSON> D    reg. hours 40.00, overtime hours 5.00\n<PERSON>   reg. hours 40.00\nCaine   reg. hours 40.00, overtime hours 5.50, commission $531.00\nNate     reg. hours 40.00, overtime hours 4.00, commission $198.00\nPaul     reg. hours 40.00, overtime hours 2.50"}, "subject": {"text": "Payroll - Mozart Towing Week Ending 11/17/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_035", "id": "TEST_035", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC035"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Good Morning Chris & Briania, Payroll for Name Electrical & Construction for week ending Friday November 22, 2024\n<PERSON>    8 hours.\nThanks\n<PERSON><PERSON>"}, "subject": {"text": "Payroll - Name Electrical & Construction 11/22/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_036", "id": "TEST_036", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC036"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Please TRIPLE CHECK this as last week was WRONG and I never received a follow up call or email from either of you. Unacceptable.\n\nPayroll:\nCheyenne:\nCommission: 1,378.60\nTips: 366.18\nAssisting: 4.5\nFloor: 18.48\n\nElise:\nCommission: 2,200.40\nTips: 663.75\nAssisting: 1.5\nFloor hours: 34.7\n\n<PERSON><PERSON>:\nCommission: 266\nTips: 81.3\nAssisting hours: 30\nFloor hours: 8.4\n\n<PERSON>:\nCommission: 1,650.90\nTips: 813.26\nFloor Hours: 33.3\n\nPetra Brac:\nTips: 15.73\nAssisting: 37.42\n\n<PERSON>:\nCommission: 79.15\nTips: 57\nAssisting hours: 39.63\n\nNatalie:\nSkip salary ***\nShaant:\nSkip salary ****"}, "subject": {"text": "Payroll - Urgent Review Required"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_037", "id": "TEST_037", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC037"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $100\n\nThank you and have a great day\n\nChristine <PERSON>bet\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_038", "id": "TEST_038", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC038"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello <PERSON>,\nPlease use regular processing for payroll. DO NOT USE EXPEDITED PROCESSING.\nLet me know if there are any other charges beyond regular processing fees.\nReporting the hours of:\n1- ZAGREB, MICHELLE for pay period Nov 09-23, 2024:\nTotal work hours: 72 hours\nPlease use the hourly rate of $24/hour. Thank you!\nPaycheck account #A888-9999\n\n<PERSON>,\nMartha <PERSON>\n\nThe information contained in this message may be privileged, confidential and protected from disclosure. If the reader of this message is not the intended recipient, or an employee or agent responsible for delivering this message to the intended recipient, you are hereby notified that any dissemination, distribution or copying of this communication is strictly prohibited. If you have received this communication in error, please notify your representative immediately and delete this message from your computer. Thank you."}, "subject": {"text": "Payroll - Michelle <PERSON> Regular Processing Only"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_039", "id": "TEST_039", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC039"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello team,\nMonthly payroll for Client B999:\n<PERSON>:\nsalary $17126.52 and withhold federal tax $2938.00.\nCheck's date is November 29, 2024.\n\n<PERSON><PERSON><PERSON>,\n<PERSON>s BitTech\nBusiness Development\nPhone **************, Fax **************\n489 S Mount St, Belgium, GA 333333 USA\nwww.mcdonalds.com"}, "subject": {"text": "Payroll Submission - Client B999 (<PERSON>)"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_040", "id": "TEST_040", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC040"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello team,\nMonthly payroll for Client A111:\n<PERSON>: salary $12500.00 and withhold federal tax amount of $2937.00\nCheck's date is November 29, 2024.\n\n<PERSON><PERSON><PERSON>,\n<PERSON>s BitTech\nBusiness Development\nPhone **************, Fax **************\n489 S Mount St, Belgium, GA 333333 USA\nwww.mcdonalds.com"}, "subject": {"text": "Payroll Submission - Client A111 (<PERSON>)"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_041", "id": "TEST_041", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC041"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello team,\nMonthly payroll for Client B999:\n<PERSON>:\nsalary $17126.52 and withhold federal tax $2938.00.\nCheck's date is November 29, 2024.\n\n<PERSON><PERSON><PERSON>,\n<PERSON>s BitTech\nBusiness Development\nPhone **************, Fax **************\n489 S Mount St, Belgium, GA 333333 USA\nwww.mcdonalds.com"}, "subject": {"text": "Payroll Submission - Client B999 (<PERSON>)"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_042", "id": "TEST_042", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC042"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello team,\nMonthly payroll for Client A111:\n<PERSON>: salary $12500.00 and withhold federal tax amount of $2937.00\nCheck's date is November 29, 2024.\n\n<PERSON><PERSON><PERSON>,\n<PERSON>s BitTech\nBusiness Development\nPhone **************, Fax **************\n489 S Mount St, Belgium, GA 333333 USA\nwww.mcdonalds.com"}, "subject": {"text": "Payroll Submission - Client A111 (<PERSON>)"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_043", "id": "TEST_043", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC043"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $200\n\nThank you and have a great day\n\nChristine <PERSON>bet\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_044", "id": "TEST_044", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC044"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $400\n\nThank you and have a great day\n\nChristine <PERSON>bet\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_045", "id": "TEST_045", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC045"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $82.75\n\nThank you and have a great day\n\n<PERSON> Monroe\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_046", "id": "TEST_046", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC046"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $95.00\n\nThank you and have a great day\n\n<PERSON> Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_047", "id": "TEST_047", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC047"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $110.25\n\nThank you and have a great day\n\n<PERSON> Adams\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_048", "id": "TEST_048", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC048"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $123.50\n\nThank you and have a great day\n\n<PERSON>\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_049", "id": "TEST_049", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC049"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $136.00\n\nThank you and have a great day\n\n<PERSON>\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_050", "id": "TEST_050", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC050"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $147.25\n\nThank you and have a great day\n\n<PERSON>\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_051", "id": "TEST_051", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC051"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $158.50\n\nThank you and have a great day\n\n<PERSON>\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_052", "id": "TEST_052", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC052"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $167.75\n\nThank you and have a great day\n\n<PERSON> Gray\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_053", "id": "TEST_053", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC053"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON><PERSON> is $178.00\n\nThank you and have a great day\n\n<PERSON>la Hughes\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON><PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_054", "id": "TEST_054", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC054"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $189.25\n\nThank you and have a great day\n\nJack Irvine\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_055", "id": "TEST_055", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC055"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $200.50\n\nThank you and have a great day\n\n<PERSON>\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_056", "id": "TEST_056", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC056"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $212.75\n\nThank you and have a great day\n\n<PERSON> King\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_057", "id": "TEST_057", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC057"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $225.00\n\nThank you and have a great day\n\n<PERSON> Lewis\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_058", "id": "TEST_058", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC058"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $237.25\n\nThank you and have a great day\n\n<PERSON> Miller\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_059", "id": "TEST_059", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC059"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $248.50\n\nThank you and have a great day\n\n<PERSON>\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_060", "id": "TEST_060", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC060"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $259.75\n\nThank you and have a great day\n\n<PERSON>\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_061", "id": "TEST_061", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC061"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $271.00\n\nThank you and have a great day\n\n<PERSON> Parker\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_062", "id": "TEST_062", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC062"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $283.25\n\nThank you and have a great day\n\n<PERSON>\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_063", "id": "TEST_063", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC063"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $294.50\n\nThank you and have a great day\n\n<PERSON> Reed\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_064", "id": "TEST_064", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC064"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $305.75\n\nThank you and have a great day\n\n<PERSON> Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_065", "id": "TEST_065", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC065"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $317.00\n\nThank you and have a great day\n\n<PERSON>bet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_066", "id": "TEST_066", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC066"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> Underwood is $328.25\n\nThank you and have a great day\n\nVictoria Underwood\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_067", "id": "TEST_067", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC067"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $339.50\n\nThank you and have a great day\n\n<PERSON>\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_068", "id": "TEST_068", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC068"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $90.00\n\nThank you and have a great day\n\nAlex Morgan\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_069", "id": "TEST_069", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC069"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $93.25\n\nThank you and have a great day\n\n<PERSON> Carter\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_070", "id": "TEST_070", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC070"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $96.50\n\nThank you and have a great day\n\n<PERSON> Diaz\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_071", "id": "TEST_071", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC071"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $99.75\n\nThank you and have a great day\n\nDevon Smith\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_072", "id": "TEST_072", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC072"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\nEden Taylor is $103.00\n\nThank you and have a great day\n\nEden Taylor\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_073", "id": "TEST_073", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC073"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $106.25\n\nThank you and have a great day\n\n<PERSON> Reyes\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_074", "id": "TEST_074", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC074"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON><PERSON> is $109.50\n\nThank you and have a great day\n\n<PERSON><PERSON> Hart\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON><PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_075", "id": "TEST_075", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC075"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $112.75\n\nThank you and have a great day\n\n<PERSON>\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_076", "id": "TEST_076", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC076"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $116.00\n\nThank you and have a great day\n\n<PERSON> Walker\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_077", "id": "TEST_077", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC077"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $119.25\n\nThank you and have a great day\n\n<PERSON> Brooks\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_078", "id": "TEST_078", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC078"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $122.50\n\nThank you and have a great day\n\nKendall Price\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_079", "id": "TEST_079", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC079"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $125.75\n\nThank you and have a great day\n\n<PERSON> Patel\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_080", "id": "TEST_080", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC080"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> Reed is $129.00\n\nThank you and have a great day\n\nMorgan Reed\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_081", "id": "TEST_081", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC081"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $132.25\n\nThank you and have a great day\n\nNico Grant\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_082", "id": "TEST_082", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC082"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\nOakley Fox is $135.50\n\nThank you and have a great day\n\nOakley Fox\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - Oakley Fox 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_083", "id": "TEST_083", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC083"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $138.75\n\nThank you and have a great day\n\nPeyton Reed\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_084", "id": "TEST_084", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC084"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $142.00\n\nThank you and have a great day\n\n<PERSON> Burke\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_085", "id": "TEST_085", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC085"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $145.25\n\nThank you and have a great day\n\n<PERSON> Hughes\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_086", "id": "TEST_086", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC086"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\nSydney Evans is $148.50\n\nThank you and have a great day\n\nSydney Evans\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_087", "id": "TEST_087", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC087"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $151.75\n\nThank you and have a great day\n\n<PERSON> Clark\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_088", "id": "TEST_088", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC088"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON><PERSON> is $155.00\n\nThank you and have a great day\n\n<PERSON><PERSON>\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON><PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_089", "id": "TEST_089", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC089"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $158.25\n\nThank you and have a great day\n\n<PERSON>\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_090", "id": "TEST_090", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC090"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $161.50\n\nThank you and have a great day\n\n<PERSON> Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_091", "id": "TEST_091", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC091"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON><PERSON> is $164.75\n\nThank you and have a great day\n\nXander Young\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON><PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_092", "id": "TEST_092", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC092"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $229.00\n\nThank you and have a great day\n\n<PERSON> Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_093", "id": "TEST_093", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC093"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $171.00\n\nThank you and have a great day\n\n<PERSON> Holt\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_094", "id": "TEST_094", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC094"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\nAddison Silver is $178.25\n\nThank you and have a great day\n\nAddison Silver\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_095", "id": "TEST_095", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC095"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $185.50\n\nThank you and have a great day\n\n<PERSON> Turner\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_096", "id": "TEST_096", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC096"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $192.75\n\nThank you and have a great day\n\n<PERSON> Miller\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_097", "id": "TEST_097", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC097"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $200.00\n\nThank you and have a great day\n\n<PERSON> Johnson\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_098", "id": "TEST_098", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC098"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $207.25\n\nThank you and have a great day\n\n<PERSON> Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_099", "id": "TEST_099", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC099"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $214.50\n\nThank you and have a great day\n\n<PERSON>\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_100", "id": "TEST_100", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC100"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\nHailey West is $221.75\n\nThank you and have a great day\n\nHailey West\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}]