[{"companyID": "A1", "comment": {"uid": "TEST_102", "id": "TEST_102", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC101"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Here is the payroll time for April 11th -  April 25th. Check date for April\n 30th   - THE SANCHEZ FIRM ACC101 \n-\n\n-<PERSON><PERSON><PERSON> -  <PERSON> \n\n\n-<PERSON> -  <PERSON>ary \n\n\nThanks and have a great day, \n\n<PERSON> \n[email tag2]\n[cid:image004.jpg@01DBB823.5C2004D0]\n352 N. Park Blvd., Suite 15\nBaton Rouge, LA 78602\n504-309-5000 ext. 218\b<EMAIL>"}, "subject": {"text": "Payroll Salary"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_106", "id": "TEST_106", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC105"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON>,\n\nPlease process the following payroll with a check date of Wednesday April 30th, 2025.\n\nAll regular salaries:\n\n  *   <PERSON>: $1,408.33 gross salary\n  *   <PERSON>: $1,700.00 gross salary\n  *   <PERSON>: $675.00 gross salary\n  *   <PERSON>: $650.00 gross salary\n  *   <PERSON>: $325.00 gross salary\n  *   <PERSON>: $525.00 gross salary\n\nHourly:\n\nThank you!"}, "subject": {"text": "Moss Mountain (ACC105) #client# - salary"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_109", "id": "TEST_109", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC108"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON><PERSON>\n\n\nI am emailing my payroll to you for the Law Office of <PERSON>, PLL\n\n\nthe payroll posting 5/1/25 for 4/15/25 to 4/30/25 is\n\n\n\n\n<PERSON><PERSON> No Pay\n\n<PERSON> Pay $ 2,500.00 Salary\n\nThank you\nIf you need to speak with me, My cell phone is ************.\n\n<PERSON>"}, "subject": {"text": "Aruzzo law office - salary"}}]}}]