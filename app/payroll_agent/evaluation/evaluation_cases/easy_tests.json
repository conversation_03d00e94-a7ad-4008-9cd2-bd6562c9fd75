[{"companyID": "A1", "comment": {"uid": "TEST_001", "id": "TEST_001", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC001"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hi <PERSON><PERSON> and <PERSON>,\nPlease process this payroll. It can be processed for and paid tomorrow or Thursday.\n<PERSON><PERSON><PERSON> is DD as always.\n<PERSON>'s checks get delivered to the business address.\n<PERSON> is back to working and she is set up for Direct Deposit.\nPlease confirm receipt of this email.\n\nPay rates:\n<PERSON><PERSON><PERSON>: $15.00/hour (Front Desk/Administration)\n<PERSON>: $16.00/hour (Housekeeping)\n<PERSON>: $15.00/hour (Housekeeping)\nBintou Pear: $15.00/hour (Housekeeping)\n<PERSON>: $15/hour (Maintenance)\n\nTimecards:\nFor the week of 11/04/24 - 11/10/24\n<PERSON><PERSON><PERSON> - 16.00 hours\nKimberly - 7.75 hours\nSamatha - 0.00 hours\nBintou - 0.00 hours\nHarold - 7.75 hours\n\nFor the week of 11/11/24 - 11/17/24\nAn<PERSON>li - 16.00 hours\nKimberly - 11.25 hours\nSamantha - 0.00 hours\nBintou - 0.00 hours\n<PERSON> - 8.00 hours\n\nThank you,\nMash Fruit\nFruitcompany LLC\nSuper 8 Fruitville, NY"}, "subject": {"text": "Payroll Submission - Fruitcompany LLC"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_002", "id": "TEST_002", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC002"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON>,\nHere is the Payroll for 10-21-24 to 11-17-24 Paydate 11-26-24\n<PERSON>\n123.23 Regular hours\n0 overtime hours\n0 bonus hours\n0 vacation hours\n0 holiday hours\n<PERSON><PERSON>\n119.05 Regular hours\n0 overtime hours\n0 bonus hours\n0 vacation hours\n0 holiday hours\n<PERSON> (<PERSON><PERSON>) Alpha\n117.32 regular hours\n0 overtime hours\n0 bonus hours\n0 Vacation Hours\n0 Holiday hours\n<PERSON>\n73.15 regular hours\n0 overtime hours\n0 bonus hours\n0 Vacation Hours\n0 Holiday hours\nJordan Jett<PERSON>\n116 regular hours\n0 overtime hours\n0 Vacation Hours\n0 Holiday hours\n<PERSON>\n158.58 regular hours\n7.07 overtime hours\n0 bonus hours\n0 Vacation hours\n0 Holiday hours\nJim <PERSON>\nRegular Salary $9000\n--\nThanks,\n<PERSON>\n555-230-4444\nNu Precision Materials"}, "subject": {"text": "Payroll Submission - 10/21 to 11/17 Pay Period"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_003", "id": "TEST_003", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC003"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Good Morning! Here is payroll for Friday 11/22, please let me know if you need anything else to process this.\n<PERSON> - $78.5 regular\n\nThank you.\n<PERSON>\nAB Sign & Print"}, "subject": {"text": "Payroll for 11/22 - AB Sign & Print"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_004", "id": "TEST_004", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC003"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "<PERSON> Towing Payroll Week Ending 11/17/24\n\nRobert    reg. hours 40.00, overtime hours 5.50\nWilliam    reg. hours 40.00, overtime hours 2.50, $134.50\n<PERSON> C    reg. hours 40.00, overtime hours 4.00\n<PERSON> D    reg. hours 40.00, overtime hours 5.00\n<PERSON>   reg. hours 40.00\nCaine   reg. hours 40.00, overtime hours 5.50, commission $531.00\nNate     reg. hours 40.00, overtime hours 4.00, commission $198.00\nPaul     reg. hours 40.00, overtime hours 2.50"}, "subject": {"text": "Payroll - Mozart Towing Week Ending 11/17/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_005", "id": "TEST_005", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC003"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Good Morning Chris & Briania, Payroll for Name Electrical & Construction for week ending Friday November 22, 2024\n<PERSON>    8 hours.\nThanks\n<PERSON><PERSON>"}, "subject": {"text": "Payroll - Name Electrical & Construction 11/22/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_006", "id": "TEST_006", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC003"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Please TRIPLE CHECK this as last week was WRONG and I never received a follow up call or email from either of you. Unacceptable.\n\nPayroll:\nCheyenne:\nCommission: 1,378.60\nTips: 366.18\nAssisting: 4.5\nFloor: 18.48\n\nElise:\nCommission: 2,200.40\nTips: 663.75\nAssisting: 1.5\nFloor hours: 34.7\n\n<PERSON><PERSON>:\nCommission: 266\nTips: 81.3\nAssisting hours: 30\nFloor hours: 8.4\n\n<PERSON>:\nCommission: 1,650.90\nTips: 813.26\nFloor Hours: 33.3\n\nPetra Brac:\nTips: 15.73\nAssisting: 37.42\n\n<PERSON>:\nCommission: 79.15\nTips: 57\nAssisting hours: 39.63\n\nNatalie:\nSkip salary ***\nShaant:\nSkip salary ****"}, "subject": {"text": "Payroll - Urgent Review Required"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_007", "id": "TEST_007", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC004"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $100\n\nThank you and have a great day\n\nChristine <PERSON>bet\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_008", "id": "TEST_008", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC002"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello <PERSON>,\nPlease use regular processing for payroll. DO NOT USE EXPEDITED PROCESSING.\nLet me know if there are any other charges beyond regular processing fees.\nReporting the hours of:\n1- ZAGREB, MICHELLE for pay period Nov 09-23, 2024:\nTotal work hours: 72 hours\nPlease use the hourly rate of $24/hour. Thank you!\nPaycheck account #A888-9999\n\n<PERSON>,\nMartha <PERSON>\n\nThe information contained in this message may be privileged, confidential and protected from disclosure. If the reader of this message is not the intended recipient, or an employee or agent responsible for delivering this message to the intended recipient, you are hereby notified that any dissemination, distribution or copying of this communication is strictly prohibited. If you have received this communication in error, please notify your representative immediately and delete this message from your computer. Thank you."}, "subject": {"text": "Payroll - Michelle <PERSON> Regular Processing Only"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_009", "id": "TEST_009", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC004"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello team,\nMonthly payroll for Client B999:\n<PERSON>:\nsalary $17126.52 and withhold federal tax $2938.00.\nCheck's date is November 29, 2024.\n\n<PERSON><PERSON><PERSON>,\n<PERSON>s BitTech\nBusiness Development\nPhone **************, Fax **************\n489 S Mount St, Belgium, GA 333333 USA\nwww.mcdonalds.com"}, "subject": {"text": "Payroll Submission - Client B999 (<PERSON>)"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_010", "id": "TEST_010", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC004"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello team,\nMonthly payroll for Client A111:\n<PERSON>: salary $12500.00 and withhold federal tax amount of $2937.00\nCheck's date is November 29, 2024.\n\n<PERSON><PERSON><PERSON>,\n<PERSON>s BitTech\nBusiness Development\nPhone **************, Fax **************\n489 S Mount St, Belgium, GA 333333 USA\nwww.mcdonalds.com"}, "subject": {"text": "Payroll Submission - Client A111 (<PERSON>)"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_011", "id": "TEST_011", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC011"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello team,\nMonthly payroll for Client B999:\n<PERSON>:\nsalary $17126.52 and withhold federal tax $2938.00.\nCheck's date is November 29, 2024.\n\n<PERSON><PERSON><PERSON>,\n<PERSON>s BitTech\nBusiness Development\nPhone **************, Fax **************\n489 S Mount St, Belgium, GA 333333 USA\nwww.mcdonalds.com"}, "subject": {"text": "Payroll Submission - Client B999 (<PERSON>)"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_012", "id": "TEST_012", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC012"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello team,\nMonthly payroll for Client A111:\n<PERSON>: salary $12500.00 and withhold federal tax amount of $2937.00\nCheck's date is November 29, 2024.\n\n<PERSON><PERSON><PERSON>,\n<PERSON>s BitTech\nBusiness Development\nPhone **************, Fax **************\n489 S Mount St, Belgium, GA 333333 USA\nwww.mcdonalds.com"}, "subject": {"text": "Payroll Submission - Client A111 (<PERSON>)"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_013", "id": "TEST_013", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC004"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $200\n\nThank you and have a great day\n\nChristine <PERSON>bet\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_014", "id": "TEST_014", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC004"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $400\n\nThank you and have a great day\n\nChristine <PERSON>bet\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_015", "id": "TEST_015", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC099"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Hello payroll for 11/27/24 for\n\n<PERSON> is $400\n\nThank you and have a great day\n\nChristine <PERSON>bet\nAlphabet Transportation\n390 New Swamp Road\nHudson Falls NY 12839\nP: (518)791-8392\n"}, "subject": {"text": "Payroll - <PERSON> 11/27/24"}}]}}, {"companyID": "A1", "comment": {"uid": "TEST_003", "id": "TEST_003", "user_properties": {"string:Sender": "<EMAIL>", "string:Sender Domain": "ACC003"}, "messages": [{"from": "<EMAIL>", "to": ["<EMAIL>"], "body": {"text": "Good morning,\nHere is the payroll information for Friday, November 22:\n- <PERSON> – 40 hours regular\n- <PERSON> – 40 hours regular\n- <PERSON> 40 hours regular\n- <PERSON> – 15 per hour\n- <PERSON> – 15 per hour\n\nPlease let me know if you need anything else to process this.\n\nThank you,\n<PERSON>\nAB Sign & Print"}, "subject": {"text": "Payroll for 11/22 - AB Sign & Print"}}]}}]