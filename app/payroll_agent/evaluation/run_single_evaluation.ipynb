{"cells": [{"cell_type": "code", "execution_count": 2, "id": "6a64dd5a", "metadata": {"ExecuteTime": {"end_time": "2025-05-20T20:45:35.439358Z", "start_time": "2025-05-20T20:45:34.690031Z"}}, "outputs": [], "source": ["import json\n", "\n", "import pandas as pd\n", "import requests"]}, {"cell_type": "code", "execution_count": null, "id": "991ae094", "metadata": {"ExecuteTime": {"end_time": "2025-05-20T20:45:37.564846Z", "start_time": "2025-05-20T20:45:35.969751Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Success!\n", "{\n", "  \"response\": {\n", "    \"input_state\": {\n", "      \"IMSEPCID\": \"TEST_003\",\n", "      \"ContactType\": \"client\",\n", "      \"CustomerID\": \"ACC003\",\n", "      \"EmailContent\": \"Good Morning! Here is payroll for Friday 11/22, please let me know if you need anything else to process this.\\nJ<PERSON> - $78.5 regular\\n\\nThank you.\\nS<PERSON><PERSON>\\nAB Sign & Print\",\n", "      \"SourceAddress\": \"<EMAIL>\",\n", "      \"DestinationAddress\": \"<EMAIL>\",\n", "      \"Subject\": \"Payroll for 11/22 - AB Sign & Print\",\n", "      \"received_at\": \"2025-06-10 14:23:54\"\n", "    },\n", "    \"classification_output_state\": {\n", "      \"intent_triage_metadata\": {\n", "        \"isWorkRelated\": 100,\n", "        \"RequiresWork\": 100,\n", "        \"isAboutPayroll\": 100,\n", "        \"isOnlyAboutPayroll\": 100\n", "      },\n", "      \"intent_triage_passes_flag\": true,\n", "      \"intent_triage_passes_fail_reasons\": null,\n", "      \"l1_intent_metadata\": {\n", "        \"EnterPayroll\": 90,\n", "        \"RunPayroll\": 80,\n", "        \"ChangeEmployeeStatus\": 0,\n", "        \"SystemQuestion\": 0,\n", "        \"FixMistake\": 0,\n", "        \"AdjustWithholdings\": 0,\n", "        \"OtherPayrollRelated\": 0,\n", "        \"NotPayrollRelated\": 0,\n", "        \"NotAbleToClassifyIntent\": 0\n", "      },\n", "      \"l1_intent_passes_flag\": true,\n", "      \"l1_intent_passes_fail_reasons\": null,\n", "      \"request_type_metadata\": {\n", "        \"EmployeeCount\": \"1_employee\",\n", "        \"LineItemsPerPerson\": \"1\",\n", "        \"PayType\": \"other\",\n", "        \"Segmentation\": \"none\",\n", "        \"OrganizationOfComponents\": \"name_level\",\n", "        \"PayPeriodUnit\": \"day\",\n", "        \"WeekendsIncluded\": \"unknown\",\n", "        \"NumPayPeriods\": \"1\",\n", "        \"ReferenceToPrior\": \"none\",\n", "        \"SpecialInstructions\": \"none\",\n", "        \"RequiresCalculation\": \"none\",\n", "        \"Sentiment\": \"neutral\",\n", "        \"HasAttachments\": \"no\",\n", "        \"AttachmentType\": \"none\",\n", "        \"CallRequested\": \"no\"\n", "      },\n", "      \"complexity_metadata\": {\n", "        \"ComplexityTag\": \"easy\",\n", "        \"ComplexityTagReason\": [\n", "          \"All easy criteria matched\"\n", "        ]\n", "      },\n", "      \"termination_reason\": null,\n", "      \"termination_node\": null,\n", "      \"should_continue\": true\n", "    },\n", "    \"account_lookup_output_state\": {\n", "      \"sender_email_metadata\": {\n", "        \"sender_name\": \"<PERSON>\",\n", "        \"company_name\": \"AB Sign & Print\",\n", "        \"sender_phone_number\": \"\",\n", "        \"sender_address\": \"\",\n", "        \"employees_names\": [\n", "          \"<PERSON>\"\n", "        ]\n", "      },\n", "      \"employees_metadata\": [\n", "        \"<PERSON>\"\n", "      ],\n", "      \"account_lookup_validate_sender_metadata\": {\n", "        \"valid\": true,\n", "        \"errors\": []\n", "      },\n", "      \"client_config\": {\n", "        \"account_id\": \"ACC001\",\n", "        \"account_name\": \"Acme Corporation\",\n", "        \"emails\": [\n", "          \"<EMAIL>\",\n", "          \"<EMAIL>\",\n", "          \"<EMAIL>\"\n", "        ],\n", "        \"status\": \"Active\",\n", "        \"created_date\": \"2023-01-15\",\n", "        \"account_config_template\": {\n", "          \"Name\": \"INSERT NAME HERE\",\n", "          \"Amount\": \"INSERT AMOUNT HERE\",\n", "          \"Hours\": \"INSERT HOURS HERE\",\n", "          \"HourlyRate\": \"INSERT HOURLY RATE HERE\"\n", "        },\n", "        \"lookup_email\": \"<EMAIL>\",\n", "        \"lookup_timestamp\": \"2025-06-10T14:24:25.981614Z\",\n", "        \"lookup_status\": \"success\",\n", "        \"error\": null,\n", "        \"suggestion\": null\n", "      },\n", "      \"is_client_id_valid\": true,\n", "      \"termination_reason\": null,\n", "      \"termination_node\": null,\n", "      \"should_continue\": true\n", "    },\n", "    \"payroll_extraction_output_state\": {\n", "      \"extracted_payrolls\": {\n", "        \"payroll_entries\": [\n", "          {\n", "            \"Name\": \"<PERSON>\",\n", "            \"Amount\": \"78.5\"\n", "          }\n", "        ],\n", "        \"success\": true,\n", "        \"error\": null\n", "      },\n", "      \"extracted_payrolls_agent_1\": {\n", "        \"payroll_entries\": [\n", "          {\n", "            \"Name\": \"<PERSON>\",\n", "            \"Amount\": \"78.5\"\n", "          }\n", "        ],\n", "        \"success\": true,\n", "        \"error\": null\n", "      },\n", "      \"extracted_payrolls_agent_2\": {\n", "        \"payroll_entries\": [\n", "          {\n", "            \"Name\": \"<PERSON>\",\n", "            \"Amount\": \"78.5\"\n", "          }\n", "        ],\n", "        \"success\": true,\n", "        \"error\": null\n", "      },\n", "      \"termination_reason\": null,\n", "      \"termination_node\": null,\n", "      \"should_continue\": true\n", "    },\n", "    \"validation_output_state\": {\n", "      \"validated_payroll_entries\": {\n", "        \"validated_entries\": [\n", "          {\n", "            \"Name\": \"<PERSON>\",\n", "            \"Amount\": \"78.5\"\n", "          }\n", "        ],\n", "        \"success\": true,\n", "        \"error\": null\n", "      },\n", "      \"formatted_payroll_entries\": null,\n", "      \"termination_reason\": null,\n", "      \"termination_node\": null,\n", "      \"should_continue\": true\n", "    },\n", "    \"execution_output_state\": {\n", "      \"completed_payrolls\": [\n", "        {\n", "          \"Name\": \"<PERSON>\",\n", "          \"Amount\": \"78.5\"\n", "        }\n", "      ],\n", "      \"completed_payrolls_flag\": true,\n", "      \"termination_reason\": null,\n", "      \"termination_node\": null,\n", "      \"should_continue\": true\n", "    },\n", "    \"release_output_state\": {\n", "      \"proceed_without_verification\": {\n", "        \"status\": true,\n", "        \"errors\": null\n", "      },\n", "      \"release_payroll\": true,\n", "      \"release_dashboard_logging\": true,\n", "      \"release_update_upstream_ticket\": true,\n", "      \"release_send_confirmation\": true,\n", "      \"release_create_summary\": \"**Payroll Summary Report**\\n\\n**Report Date and Source:**\\n- Report Date: Not explicitly provided, inferred from email content as November 22.\\n- Source Email: <EMAIL>\\n\\n**Employee Processing:**\\n- Total Number of Employees Processed: 1\\n\\n**Aggregate Payroll Information:**\\n- Total Hours Worked: Not specified in the data.\\n- Total Payroll Cost: $78.50\\n\\n**Breakdown by Department and Project:**\\n- Department and project-specific details are not available in the provided data.\\n\\n**Anomalies and Validation Flags:**\\n- No anomalies or validation flags were reported in the payroll extraction and validation process.\\n\\n**Missing or Empty Fields:**\\n- Hours worked, hourly rate, and department/project breakdowns are missing from the data.\\n- No detailed breakdown of hours or costs by department or project is available.\\n\\nThis summary reflects the payroll data processed for <PERSON>, with a total payroll cost of $78.50. The report lacks detailed information on hours worked and departmental or project allocations.\",\n", "      \"termination_reason\": null,\n", "      \"termination_node\": null,\n", "      \"should_continue\": true\n", "    },\n", "    \"started_at\": \"2025-06-10 14:23:54\",\n", "    \"finished_at\": \"2025-06-10 14:24:55\",\n", "    \"ticket_id\": \"TEST_003\",\n", "    \"termination_flag\": false,\n", "    \"successful_flag\": true\n", "  }\n", "}\n"]}], "source": ["\n", "# Set the endpoint\n", "url = \"http://localhost:8000/api/v1/process-email\"\n", "\n", "# Define one test case (you can also load from file)\n", "test_case =         {\n", "    \"IMSEPCID\": \"TEST_003\",\n", "    \"ContactType\": \"client\",\n", "    \"CustomerID\": \"ACC003\",\n", "    \"EmailContent\": \"Good Morning! Here is payroll for Friday 11/22, please let me know if you need anything else to process this.\\nJ<PERSON> - 78.5 regular\\n\\nThank you.\\n<PERSON><PERSON><PERSON>\\nAB Sign & Print\",\n", "    \"SourceAddress\": \"<EMAIL>\",\n", "    \"DestinationAddress\": \"<EMAIL>\",\n", "    \"Subject\": \"Payroll for 11/22 - AB Sign & Print\"\n", "  }\n", "\n", "test_case =     {\n", "    \"IMSEPCID\": \"TEST_001\",\n", "    \"ContactType\": \"client\",\n", "    \"CustomerID\": \"ACC001\",\n", "    \"EmailContent\": \"<PERSON> <PERSON><PERSON> and <PERSON>,\\nPlease process this payroll. It can be processed for and paid tomorrow or Thursday.\\n<PERSON><PERSON><PERSON> is DD as always.\\n<PERSON><PERSON><PERSON>'s checks get delivered to the business address.\\n<PERSON><PERSON><PERSON><PERSON> is back to working and she is set up for Direct Deposit.\\nPlease confirm receipt of this email.\\n\\nPay rates:\\nAnjali Apple: $15.00/hour (Front Desk/Administration)\\nKimberly Banana: $16.00/hour (Housekeeping)\\nSamatha Citrus: $15.00/hour (Housekeeping)\\nBintou Pear: $15.00/hour (Housekeeping)\\nHarold Tomato: $15/hour (Maintenance)\\n\\nTimecards:\\nFor the week of 11/04/24 - 11/10/24\\nAnjali - 16.00 hours\\nKimberly - 7.75 hours\\nSamatha - 0.00 hours\\nBintou - 0.00 hours\\nHarold - 7.75 hours\\n\\nFor the week of 11/11/24 - 11/17/24\\nAnjali - 16.00 hours\\nKimberly - 11.25 hours\\nSamantha - 0.00 hours\\nBintou - 0.00 hours\\nHarold - 8.00 hours\\n\\nThank you,\\nMash Fruit\\nFruitcompany LLC\\nSuper 8 Fruitville, NY\",\n", "    \"SourceAddress\": \"<EMAIL>\",\n", "    \"DestinationAddress\": \"<EMAIL>\",\n", "    \"Subject\": \"Payroll Submission - Fruitcompany LLC\"\n", "  },\n", "\n", "# Make the request\n", "response = requests.post(url, json=test_case)\n", "\n", "# Handle response\n", "if response.ok:\n", "    print(\"✅ Success!\")\n", "    print(json.dumps(response.json(), indent=2))\n", "else:\n", "    print(\"❌ Error:\")\n", "    print(response.status_code, response.text)"]}, {"cell_type": "code", "execution_count": null, "id": "b16f16af", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 3, "id": "2f0e63bd", "metadata": {"ExecuteTime": {"end_time": "2025-05-20T20:45:37.704747Z", "start_time": "2025-05-20T20:45:37.702195Z"}}, "outputs": [], "source": ["def flatten_dict(d, parent_key='', sep='.'):\n", "    \"\"\"Recursively flattens nested dicts with dot notation.\"\"\"\n", "    items = []\n", "    for k, v in d.items():\n", "        new_key = f\"{parent_key}{sep}{k}\" if parent_key else k\n", "        if isinstance(v, dict):\n", "            items.extend(flatten_dict(v, new_key, sep=sep).items())\n", "        elif isinstance(v, list) and all(isinstance(i, dict) for i in v):\n", "            for i, subdict in enumerate(v):\n", "                items.extend(flatten_dict(subdict, f\"{new_key}[{i}]\", sep=sep).items())\n", "        else:\n", "            items.append((new_key, v))\n", "    return dict(items)"]}, {"cell_type": "code", "execution_count": 4, "id": "51768a0d", "metadata": {"ExecuteTime": {"end_time": "2025-05-20T20:45:39.000498Z", "start_time": "2025-05-20T20:45:38.982843Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>input_state.IMSEPCID</th>\n", "      <th>input_state.ContactType</th>\n", "      <th>input_state.CustomerID</th>\n", "      <th>input_state.EmailContent</th>\n", "      <th>input_state.SourceAddress</th>\n", "      <th>input_state.DestinationAddress</th>\n", "      <th>input_state.Subject</th>\n", "      <th>input_state.received_at</th>\n", "      <th>intake_output_state.sender_email_metadata.sender_name</th>\n", "      <th>intake_output_state.sender_email_metadata.company_name</th>\n", "      <th>...</th>\n", "      <th>ai_processing_output_state.termination_node</th>\n", "      <th>ai_processing_output_state.should_continue</th>\n", "      <th>started_at</th>\n", "      <th>finished_at</th>\n", "      <th>ticket_id</th>\n", "      <th>termination_flag</th>\n", "      <th>successful_flag</th>\n", "      <th>termination_dict.termination_graph</th>\n", "      <th>termination_dict.termination_reason</th>\n", "      <th>termination_dict.termination_node</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TEST_001</td>\n", "      <td>client</td>\n", "      <td>ACC001</td>\n", "      <td>Hello payroll for 11/27/24 for <PERSON> is $10...</td>\n", "      <td><EMAIL></td>\n", "      <td><EMAIL></td>\n", "      <td>Payroll Submission</td>\n", "      <td>2025-05-20 20:45:35</td>\n", "      <td><PERSON></td>\n", "      <td>Flores Transportation</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>False</td>\n", "      <td>2025-05-20 20:45:35</td>\n", "      <td>2025-05-20 20:45:37</td>\n", "      <td>TEST_001</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>intake_output_state</td>\n", "      <td>Payroll is for multiple clients, number of cli...</td>\n", "      <td>terminate_intake</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 50 columns</p>\n", "</div>"], "text/plain": ["  input_state.IMSEPCID input_state.ContactType input_state.CustomerID  \\\n", "0             TEST_001                  client                 ACC001   \n", "\n", "                            input_state.EmailContent  \\\n", "0  Hello payroll for 11/27/24 for <PERSON> is $10...   \n", "\n", "  input_state.SourceAddress input_state.DestinationAddress  \\\n", "0         <EMAIL>            <EMAIL>   \n", "\n", "  input_state.Subject input_state.received_at  \\\n", "0  Payroll Submission     2025-05-20 20:45:35   \n", "\n", "  intake_output_state.sender_email_metadata.sender_name  \\\n", "0                                           <PERSON>      \n", "\n", "  intake_output_state.sender_email_metadata.company_name  ...  \\\n", "0                              Flores Transportation      ...   \n", "\n", "  ai_processing_output_state.termination_node  \\\n", "0                                        None   \n", "\n", "  ai_processing_output_state.should_continue           started_at  \\\n", "0                                      False  2025-05-20 20:45:35   \n", "\n", "           finished_at  ticket_id termination_flag successful_flag  \\\n", "0  2025-05-20 20:45:37   TEST_001             True           False   \n", "\n", "  termination_dict.termination_graph  \\\n", "0                intake_output_state   \n", "\n", "                 termination_dict.termination_reason  \\\n", "0  Payroll is for multiple clients, number of cli...   \n", "\n", "  termination_dict.termination_node  \n", "0                  terminate_intake  \n", "\n", "[1 rows x 50 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["response_data = response.json()\n", "\n", "# Step 2: Decode the nested string\n", "parsed = response_data[\"response\"]\n", "\n", "# Step 3: Flatten the parsed dictionary\n", "flat = flatten_dict(parsed)\n", "\n", "# Step 4: Convert to DataFrame\n", "df_flat = pd.DataFrame([flat])\n", "df_flat.head()"]}, {"cell_type": "code", "execution_count": null, "id": "51d13015", "metadata": {"ExecuteTime": {"end_time": "2025-05-20T20:45:39.999191Z", "start_time": "2025-05-20T20:45:39.997391Z"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1cbc913189aa62bd", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "asurion", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.3"}}, "nbformat": 4, "nbformat_minor": 5}