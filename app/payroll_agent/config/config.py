import httpx

from functools import lru_cache
from typing import Dict, List, Literal, Optional, ClassVar, Union
from langchain_openai import ChatOpenAI, AzureChatOpenAI

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict


LLMType = Union[ChatOpenAI, AzureChatOpenAI]


class MCPConnection(BaseModel):
    """Configuration for a single MCP server."""
    url: Optional[str] = None
    transport: Literal["streamable_http", "websocket", "stdio"]
    command: Optional[str] = None
    args: Optional[List[str]] = None

    # Mark these as Secret (and exclude them from dumps)
    authentication_url: Optional[str] = Field(None, exclude=True)
    client_id: Optional[str] = Field(None, exclude=True)
    client_secret: Optional[str] = Field(None, exclude=True)

    # Headers will be populated by Settings
    headers: Dict[str, str] = Field(default_factory=dict)


class Settings(BaseSettings):
    # API Settings
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    DEBUG: bool = False

    # OpenAI Settings
    OPENAI_API_KEY: Optional[str] = None

    # LLM Settings
    LLM_MODEL: str = "gpt-4o"
    LLM_MODEL_SECOND_AGENT : str = "gpt-4.1"
    LLM_TEMPERATURE: float = 0.0
    LLM_TYPE: Optional[str] = "OPENAI"
    API_VERSION: Optional[str] = None
    AZURE_ENDPOINT: Optional[str] = None

    _llm_instance: ClassVar[Optional[LLMType]] = None
    _alt_llm_instances: ClassVar[Dict[int, LLMType]] = {}

    # LangChain Settings
    LANGCHAIN_API_KEY: Optional[str] = None
    LANGCHAIN_TRACING_V2: Optional[str] = None
    LANGCHAIN_ENDPOINT: Optional[str] = None
    LANGCHAIN_PROJECT: Optional[str] = None

    # MCP Settings
    mcp_servers: Dict[str, MCPConnection] = Field(
        default_factory=dict,
        description="Mapping of server name to MCPConnection configs"
    )

    # Pydantic Settings configuration
    model_config = SettingsConfigDict(
        env_file='.env',
        case_sensitive=False,
        env_nested_delimiter='__',
        ignored_types=(ChatOpenAI, AzureChatOpenAI)
    )

    def __init__(self, **data):
        super().__init__(**data)
        # right after we’re built, fetch each token sync-ly
        self._populate_mcp_headers_sync()

    def _populate_mcp_headers_sync(self) -> None:
        for conn in self.mcp_servers.values():
            if conn.client_id and conn.client_secret and conn.authentication_url:
                token = self._fetch_oauth_token_sync(
                    conn.authentication_url,
                    conn.client_id,
                    conn.client_secret,
                )
                conn.headers["Authorization"] = f"Bearer {token}"

    @staticmethod
    def _fetch_oauth_token_sync(auth_url: str , client_id: str, client_secret: str) -> str:
        print("credentials ", client_id, client_secret)
        with httpx.Client() as client:
            resp = client.post(
                auth_url,
                data={
                    "username": client_id,
                    "password": client_secret,
                    "scope": ""
                },
            )
            resp.raise_for_status()
            return resp.json()["access_token"]
        
    def LLM(self, agent_number: int = 1) -> LLMType:
        """Get an LLM instance for the specified agent number.
        
        Args:
            agent_number: The agent number (1 for primary, others for secondary agents)
            
        Returns:
            An instance of ChatOpenAI or AzureChatOpenAI
        """
        if agent_number == 1:
            if not type(self)._llm_instance:
                type(self)._llm_instance = self._create_llm(model=self.LLM_MODEL)
            return type(self)._llm_instance
        else:
            if agent_number not in type(self)._alt_llm_instances:
                # Use LLM_MODEL_SECOND_AGENT instead of self.LLM
                type(self)._alt_llm_instances[agent_number] = self._create_llm(model=self.LLM_MODEL_SECOND_AGENT)
            return type(self)._alt_llm_instances[agent_number]
        
    def _create_llm(self, model: str) -> LLMType:
        """Create an LLM instance with the specified model.
        
        Args:
            model: The model name to use (e.g., "gpt-4o", "gpt-4.1", etc.)
            
        Returns:
            An instance of ChatOpenAI or AzureChatOpenAI
        """
        if self.LLM_TYPE == "AZURE":
            # Check if we should use the Paychex-specific endpoint format
            if self.AZURE_ENDPOINT and "service-internal-n2a.paychex.com" in self.AZURE_ENDPOINT:
                # Use the Paychex-specific endpoint format
                return AzureChatOpenAI(
                    api_version=self.API_VERSION or "2024-02-01",
                    azure_endpoint=f"{self.AZURE_ENDPOINT}/eps/shared/azure/openai/deployments/{model}",
                    model=model,
                    temperature=self.LLM_TEMPERATURE,
                    api_key=self.OPENAI_API_KEY,
                )
            else:
                # Use the standard Azure OpenAI format
                return AzureChatOpenAI(
                    api_version=self.API_VERSION or "2024-02-01",
                    azure_endpoint=self.AZURE_ENDPOINT,
                    model=model,
                    temperature=self.LLM_TEMPERATURE,
                    api_key=self.OPENAI_API_KEY,
                )
        elif self.LLM_TYPE == "OPENAI":
            return ChatOpenAI(
                model=model,
                temperature=self.LLM_TEMPERATURE,
                api_key=self.OPENAI_API_KEY,
            )
        else:
            raise ValueError(f"Unsupported LLM_TYPE: {self.LLM_TYPE}")

    '''
    @property
    def LLM(self) -> Union[ChatOpenAI, AzureChatOpenAI]:
        if not type(self)._llm_instance:
            if self.LLM_TYPE == "AZURE":
                type(self)._llm_instance = AzureChatOpenAI(
                    api_version=self.API_VERSION,
                    azure_endpoint=self.AZURE_ENDPOINT,
                    model=self.LLM_MODEL,
                    temperature=self.LLM_TEMPERATURE,
                    api_key=self.OPENAI_API_KEY,
                )
            elif self.LLM_TYPE == "OPENAI":
                type(self)._llm_instance = ChatOpenAI(
                    model=self.LLM_MODEL,
                    temperature=self.LLM_TEMPERATURE,
                    api_key=self.OPENAI_API_KEY,
                )
            else:
                raise ValueError(f"Unsupported LLM_TYPE: {self.LLM_TYPE}")
        return type(self)._llm_instance
    '''
                

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls,
        init_settings,
        env_settings,
        dotenv_settings,
        file_secret_settings,
    ):
        # Override to prioritize .env file over environment variables
        return (
            dotenv_settings,  # .env file has higher priority than env vars
            init_settings,
            env_settings,
            file_secret_settings,
        )

@lru_cache()
def get_settings() -> Settings:
    """Get the settings instance."""
    return Settings()


settings = get_settings()
