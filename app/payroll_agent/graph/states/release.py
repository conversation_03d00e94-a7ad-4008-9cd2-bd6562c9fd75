from typing import Optional
from pydantic import BaseModel, Field

from app.payroll_agent.models.release import ProceedWithoutVerificationMetadata


class ReleaseOutputState(BaseModel):
    """Raw input state containing the email content and metadata"""
    proceed_without_verification: ProceedWithoutVerificationMetadata = Field(default_factory=ProceedWithoutVerificationMetadata, description="Whether to proceed without verification")
    release_payroll: Optional[bool] = Field(None, description="Whether to release payroll")
    release_dashboard_logging: Optional[bool] = Field(None, description="Whether to log to the dashboard")
    release_update_upstream_ticket: Optional[bool] = Field(None, description="Whether to update the upstream ticket")
    release_send_confirmation: Optional[bool] = Field(None, description="Whether to send confirmation")
    release_create_summary: str = Field(None, description="Summary of the release process")
    termination_reason: Optional[str] = Field(None, description="Reason for termination")
    termination_node: Optional[str] = Field(None, description="Node name of the termination node")
    should_continue: bool = Field(False, description="Whether the payroll app should continue to the next sub graph")