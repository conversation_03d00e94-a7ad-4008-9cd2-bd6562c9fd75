from pydantic import BaseModel, Field

from app.payroll_agent.graph.states.classification import InputState, ClassificationOutputState
from app.payroll_agent.graph.states.company_worker_lookup import CompanyWorkerLookupOutputState
from app.payroll_agent.graph.states.payroll_processing import PayrollProcessingOutputState
from app.payroll_agent.graph.states.validation import ValidationOutputState
from app.payroll_agent.graph.states.execution import ExecutionOutputState
from app.payroll_agent.graph.states.release import ReleaseOutputState


class PayrollState(BaseModel):
    """Formatted output state containing the processed payroll entries"""
    input_state: InputState = Field(default_factory=InputState, description="JSON payload")
    classification_output_state: ClassificationOutputState = Field(default_factory=ClassificationOutputState, description="classification_output_state")
    company_worker_lookup_output_state: CompanyWorkerLookupOutputState = Field(default_factory=CompanyWorkerLookupOutputState, description="company_worker_lookup_output_state")
    payroll_processing_output_state: PayrollProcessingOutputState = Field(default_factory=PayrollProcessingOutputState, description="payroll_extraction_output_state")
    validation_output_state: ValidationOutputState = Field(default_factory=ValidationOutputState, description="validation_output_state")
    execution_output_state: ExecutionOutputState = Field(default_factory=ExecutionOutputState, description="execution_output_state")
    release_output_state: ReleaseOutputState = Field(default_factory=ReleaseOutputState, description="release_output_state")