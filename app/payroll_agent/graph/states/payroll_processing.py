from typing import Optional, List, Dict
from pydantic import BaseModel, Field

from app.payroll_agent.models.payroll_processing import PayrollEntries


class PayrollProcessingOutputState(BaseModel):
    """Raw input state containing the email content and metadata"""
    company_checks: List[Dict] = Field(default_factory=list, description="company checks")
    payroll_commands_agent_1: List[Dict] = Field(default_factory=list, description="payroll commands from agent 1")
    payroll_commands_agent_2: List[Dict] = Field(default_factory=list, description="payroll commands from agent 2")
    extracted_payrolls: PayrollEntries = Field(default_factory=PayrollEntries, description="final extracted payroll_entries")
    extracted_payrolls_agent_1: PayrollEntries = Field(default_factory=PayrollEntries, description="payroll_entries_from_agent_1")
    extracted_payrolls_agent_2: PayrollEntries = Field(default_factory=PayrollEntries, description="payroll_entries_from_agent_2")
    termination_reason: Optional[str] = Field(None, description="Reason for termination")
    termination_node: Optional[str] = Field(None, description="Node name of the termination node")
    should_continue: bool = Field(False, description="Whether the payroll app should continue to the next sub graph")