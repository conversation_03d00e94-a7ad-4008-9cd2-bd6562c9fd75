from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Any, Optional, Literal, Union

from app.payroll_agent.models.company_worker_lookup import SenderEmailMetadata, ValidateSenderMetadata, WorkersMatch


class ClientConfig(BaseModel):
    """Client-specific configuration and format requirements"""
    account_id: Optional[Union[str, List[str]]] = Field(None, description="Client account ID")
    account_name: Optional[str] = Field(None, description="Client account name")
    emails: Optional[List[str]] = Field(None, description="List of email addresses associated with the client")
    status: Optional[str] = Field(None, description="Status of the client")
    created_date: Optional[str] = Field(None, description="Date when the client was created")
    account_config_template: Optional[Dict[str,Any]] = Field(None, description="Template for account configuration")
    lookup_email: Optional[str]= Field(None, description="Lookup email address")
    lookup_timestamp: Optional[str] = Field(None, description="Timestamp of the lookup")
    lookup_status: Literal["success", "not_found"] = Field("not_found", description="Status of the lookup")
    error: Optional[str] = Field(None, description="Error message if any")
    suggestion: Optional[str] = Field(None, description="Suggestion message if any")


class CompanyWorkerLookupOutputState(BaseModel):
    """Raw input state containing the email content and metadata"""
    sender_email_metadata: Optional[SenderEmailMetadata] = Field(None, description="Metadata about the sender")
    employees_metadata: Optional[list[str]] = Field(None, description="List of names of the clients")
    validate_sender_metadata: Optional[ValidateSenderMetadata] = Field(None, description="Validation about the sender")
    company_lookup: Optional[dict] = Field(None, description="Company lookup results")
    workers_roster: Optional[dict] = Field(None, description="Workers lookup results")
    llm_workers_match: Optional[WorkersMatch] = Field(None, description="List of matched workers")
    workers_matched: Optional[dict] = Field(None, description="Dict of matched workers")
    pay_periods: Optional[dict] = Field(None, description="Last 3 payroll periods")
    payperiodID: Optional[str] = Field(None, description="Payperiod ID")
    client_config: Optional[ClientConfig] = Field(default_factory=ClientConfig, description="Client ID and its configuration")
    is_client_id_valid: Optional[bool] = Field(False, description="Whether the client ID is unique and equal to the hint ID")
    termination_reason: Optional[str] = Field(None, description="Reason for termination")
    termination_node: Optional[str] = Field(None, description="Node name of the termination node")
    should_continue: bool = Field(False, description="Whether the payroll app should continue to the next sub graph")

