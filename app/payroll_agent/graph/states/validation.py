from typing import Optional, List
from pydantic import BaseModel, Field

from app.payroll_agent.models.validation import ValidatedPayrollEntries


class ValidationOutputState(BaseModel):
    """Raw input state containing the email content and metadata"""
    validated_payroll_entries: ValidatedPayrollEntries = Field(default_factory=ValidatedPayrollEntries, description="validated payroll entries")
    formatted_payroll_entries: Optional[List] = Field(None, description="formatted payroll entries")
    termination_reason: Optional[str] = Field(None, description="Reason for termination")
    termination_node: Optional[str] = Field(None, description="Node name of the termination node")
    should_continue: bool = Field(False, description="Whether the payroll app should continue to the next sub graph")