from typing import Optional, List
from pydantic import BaseModel, Field


class ExecutionOutputState(BaseModel):
    """Raw input state containing the email content and metadata"""
    completed_payrolls: Optional[List] = Field(None, description="payroll_entries")
    completed_payrolls_flag: Optional[bool] = Field(None, description="Whether to payrolls are completed")
    termination_reason: Optional[str] = Field(None, description="Reason for termination")
    termination_node: Optional[str] = Field(None, description="Node name of the termination node")
    should_continue: bool = Field(False, description="Whether the payroll app should continue to the next sub graph")