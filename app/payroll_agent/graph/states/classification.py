from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field

from app.api.models.input import UpstreamModel
from app.payroll_agent.models.classification import IntentTriage, L1Intents, RequestTypeTags, ComplexityTags


class InputState(BaseModel):
    """Raw input state containing the email content and metadata"""
    companyID: str = Field(..., description="company id")
    uid: str = Field(..., description="uid")
    id: str = Field(..., description="id")
    CustomerDomain: str = Field(..., description="Domain to look up in the database")
    EmailContent: str = Field(..., description="The email content containing payroll information")
    SourceAddress: str = Field(description="Email sender address")
    DestinationAddress: List[str] = Field(...,description="Email destination address")
    Subject: str = Field(description="Email subject line")
    Payload: UpstreamModel = Field(..., description="Upstream model containing the email content and metadata")
    received_at: str = Field(default_factory=lambda: datetime.utcnow().strftime('%Y-%m-%d %T'), description="Timestamp of when the email was received")



class ClassificationOutputState(BaseModel):
    """Raw input state containing the email content and metadata"""
    intent_triage_metadata: IntentTriage = Field(None, description="Is intent triage and inital filtering metadata")
    intent_triage_passes_flag: bool = Field(None, description="Is intent triage metadata")
    intent_triage_passes_fail_reasons: Optional[list[str]] = Field(None, description="Optional: log which ones failed")
    l1_intent_metadata: L1Intents = Field(None, description="Is l1_intent metadata")
    l1_intent_passes_flag: bool = Field(None, description="Is only l1 in scope payroll related")
    l1_intent_passes_fail_reasons: Optional[list[str]] = Field(None, description="Optional: log which ones failed")
    request_type_metadata: Optional[RequestTypeTags] = Field(None, description="Structured metadata describing how the request is formatted and structured.")
    complexity_metadata: Optional[ComplexityTags] = Field(None, description="Final complexity classification based on request type metadata (easy, medium, hard).")
    termination_reason: Optional[str] = Field(None, description="Reason for termination")
    termination_node: Optional[str] = Field(None, description="Node name of the termination node")
    should_continue: bool = Field(False, description="Whether the payroll app should continue to the next sub graph")
