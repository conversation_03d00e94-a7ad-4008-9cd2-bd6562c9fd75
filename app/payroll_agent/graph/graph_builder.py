from langgraph.graph import END, START, Graph, StateGraph

from app.payroll_agent.graph.states.common import PayrollState


from app.payroll_agent.nodes.classification import (classification_intent_triage,
                                                    classification_l1_intent,
                                                    classification_l1_intent_router,
                                                    classification_intent_triage_router,
                                                    classification_request_type,
                                                    classification_complexity,
                                                    classification_terminate,
                                                    classification_finish_node)

from app.payroll_agent.nodes.company_worker_lookup import (company_worker_lookup_identify_sender,
                                                           company_worker_lookup_validate_sender,
                                                           company_worker_lookup_retrieve_company_info,
                                                           company_worker_lookup_retrieve_worker_info,
                                                           company_worker_lookup_get_payperiodID,
                                                           company_worker_lookup_get_client_id_and_config,
                                                           company_worker_lookup_terminate,
                                                           company_worker_lookup_router,
                                                           company_worker_lookup_finishing_node)

from app.payroll_agent.nodes.payroll_processing import (payroll_processing_get_company_checks,
                                                        payroll_processing_agent_run,
                                                        payroll_processing_agent_2_run,
                                                        payroll_processing_finishing_node,
                                                        payroll_processing_router,
                                                        payroll_processing_run_agent,
                                                        payroll_processing_run_agent_2,
                                                        payroll_processing_terminate)

from app.payroll_agent.nodes.validation import (validation_run_agent,
                                                validation_finishing_node,
                                                validation_terminate,
                                                validation_router,)

from app.payroll_agent.nodes.execution import (execution_create_payrolls,
                                               execution_success_payroll_router,
                                               execution_terminate,
                                               execution_finishing_node)

from app.payroll_agent.nodes.release import (release_proceed_without_verification,
                                             release_payroll,
                                             release_router,
                                             release_terminate,
                                             release_dashboard_logging,
                                             release_create_summary,
                                             release_update_upstream_ticket,
                                             release_send_confirmation,release_finishing_node)


# Create subgraphs for different stages of the workflow
# Each subgraph can be used independently or as part of the main graph
def create_classification_subgraph() -> Graph:
    workflow = StateGraph(PayrollState, output=PayrollState)
    # add nodes
    workflow.add_node("classification__intent_triage", classification_intent_triage)
    workflow.add_node("classification__intent_triage_router", classification_intent_triage_router)
    workflow.add_node("classification__l1_intent", classification_l1_intent)
    workflow.add_node("classification__l1_intent_router", classification_l1_intent_router)
    workflow.add_node("classification__request_type", classification_request_type)
    workflow.add_node("classification__complexity", classification_complexity)
    workflow.add_node("classification__terminate", classification_terminate)
    workflow.add_node("classification__finish_node", classification_finish_node)

    # add edges
    workflow.add_edge(START, "classification__intent_triage")
    workflow.add_edge("classification__intent_triage", "classification__intent_triage_router")

    # Stop  if triage fails
    workflow.add_conditional_edges(
        "classification__intent_triage_router",
        lambda x: x.classification_output_state.intent_triage_passes_flag,
        {
            True: "classification__l1_intent",
            False: "classification__terminate"
        }
    )

    # Stop if l1 intent is not in scope
    workflow.add_edge("classification__l1_intent", "classification__l1_intent_router")
    workflow.add_conditional_edges(
        "classification__l1_intent_router",
        lambda x: x.classification_output_state.l1_intent_passes_flag,
        {
            True: "classification__request_type",
            False: "classification__terminate"
        }
    )
    workflow.add_edge("classification__request_type", "classification__complexity")
    workflow.add_edge("classification__complexity", "classification__finish_node")
    workflow.add_edge("classification__terminate", END)
    workflow.add_edge("classification__finish_node", END)

    return workflow.compile()


def create_company_worker_lookup_subgraph() -> Graph:
    workflow = StateGraph(PayrollState, output=PayrollState)
    # add nodes
    workflow.add_node("company_worker_lookup__identify_sender", company_worker_lookup_identify_sender)
    workflow.add_node("company_worker_lookup__validate_sender", company_worker_lookup_validate_sender)
    workflow.add_node("company_worker_lookup__retrieve_company_info", company_worker_lookup_retrieve_company_info)
    workflow.add_node("company_worker_lookup__retrieve_worker_info", company_worker_lookup_retrieve_worker_info)
    workflow.add_node("company_worker_lookup__get_payperiodID", company_worker_lookup_get_payperiodID)
    workflow.add_node("company_worker_lookup__get_client_config", company_worker_lookup_get_client_id_and_config)
    workflow.add_node("company_worker_lookup__router", company_worker_lookup_router)
    workflow.add_node("company_worker_lookup__finish_node", company_worker_lookup_finishing_node)
    workflow.add_node("company_worker_lookup__terminate", company_worker_lookup_terminate)

    # add edges
    workflow.add_edge(START, "company_worker_lookup__identify_sender")
    workflow.add_edge("company_worker_lookup__identify_sender", "company_worker_lookup__validate_sender")
    workflow.add_edge("company_worker_lookup__validate_sender", "company_worker_lookup__retrieve_company_info")
    workflow.add_edge("company_worker_lookup__retrieve_company_info", "company_worker_lookup__retrieve_worker_info")
    workflow.add_edge("company_worker_lookup__retrieve_worker_info", "company_worker_lookup__get_payperiodID")
    workflow.add_edge("company_worker_lookup__get_payperiodID", "company_worker_lookup__get_client_config")
    workflow.add_edge("company_worker_lookup__get_client_config", "company_worker_lookup__router")
    workflow.add_conditional_edges(
        "company_worker_lookup__router",
        lambda x: x.company_worker_lookup_output_state.is_client_id_valid,
        {
            True: "company_worker_lookup__finish_node",
            False: "company_worker_lookup__terminate"
        }
    )
    workflow.add_edge("company_worker_lookup__terminate", END)
    workflow.add_edge("company_worker_lookup__finish_node", END)

    return workflow.compile()


def create_payroll_processing_subgraph() -> Graph:
    workflow = StateGraph(PayrollState, output=PayrollState)
    # add nodes
    workflow.add_node("payroll_processing_get_company_checks", payroll_processing_get_company_checks)
    workflow.add_node("payroll_processing_payroll_agent_1_run", payroll_processing_agent_run)
    workflow.add_node("payroll_processing_payroll_agent_2_run", payroll_processing_agent_2_run)
    workflow.add_node("payroll_processing_payroll_agent_1", payroll_processing_run_agent)
    workflow.add_node("payroll_processing_payroll_agent_2", payroll_processing_run_agent_2)
    workflow.add_node("payroll_processing_router", payroll_processing_router)
    workflow.add_node("payroll_processing_terminate", payroll_processing_terminate)
    workflow.add_node("payroll_processing_finishing_node", payroll_processing_finishing_node)

    # add edges
    workflow.add_edge(START, "payroll_processing_get_company_checks")
    workflow.add_edge("payroll_processing_get_company_checks", "payroll_processing_payroll_agent_1_run")
    workflow.add_edge("payroll_processing_get_company_checks", "payroll_processing_payroll_agent_2_run")
    workflow.add_edge('payroll_processing_payroll_agent_1_run', "payroll_processing_payroll_agent_1")
    workflow.add_edge('payroll_processing_payroll_agent_1_run', "payroll_processing_payroll_agent_2")
    workflow.add_edge(["payroll_processing_payroll_agent_1", "payroll_processing_payroll_agent_2"], "payroll_processing_router")
    workflow.add_conditional_edges("payroll_processing_router",
        lambda x: x.payroll_processing_output_state.extracted_payrolls.success,
        {
            True: "payroll_processing_finishing_node",
            False: "payroll_processing_terminate"
        }
    )
    workflow.add_edge("payroll_processing_terminate", END)
    workflow.add_edge("payroll_processing_finishing_node", END)

    return workflow.compile()


def create_validation_subgraph() -> Graph:
    workflow = StateGraph(PayrollState, output=PayrollState)
    # add nodes
    workflow.add_node("validation_agent", validation_run_agent)
    workflow.add_node("validation_router", validation_router)
    workflow.add_node("validation_terminate", validation_terminate)
    workflow.add_node("validation_finishing_node", validation_finishing_node)

    # add edges
    workflow.add_edge(START, "validation_agent")
    workflow.add_edge("validation_agent", "validation_router")
    workflow.add_conditional_edges("validation_router",
        lambda x: x.validation_output_state.validated_payroll_entries.success,
        {
            True: "validation_finishing_node",
            False: "validation_terminate"
        }
    )
    workflow.add_edge("validation_terminate", END)
    workflow.add_edge("validation_finishing_node", END)

    return workflow.compile()


def create_execution_subgraph() -> Graph:
    workflow = StateGraph(PayrollState, output=PayrollState)
    # add nodes
    workflow.add_node("execution_create_payrolls", execution_create_payrolls)
    workflow.add_node("execution_execution_success_payroll_router", execution_success_payroll_router)
    workflow.add_node("execution_terminate", execution_terminate)
    workflow.add_node("execution_finishing_node", execution_finishing_node)

    # add edges
    workflow.add_edge(START, "execution_create_payrolls")
    workflow.add_edge("execution_create_payrolls", "execution_execution_success_payroll_router")
    workflow.add_conditional_edges("execution_execution_success_payroll_router",
        lambda x: x.execution_output_state.completed_payrolls_flag,
            {
                True: "execution_finishing_node",
                False: "execution_terminate"
            }
    )
    workflow.add_edge("execution_terminate", END)
    workflow.add_edge("execution_finishing_node", END)

    return workflow.compile()


def create_release_subgraph() -> Graph:
    workflow = StateGraph(PayrollState, output=PayrollState)
    # add nodes
    workflow.add_node("release_proceed_without_verification", release_proceed_without_verification)
    workflow.add_node("release_payroll", release_payroll)
    workflow.add_node("release_router", release_router)
    workflow.add_node("release_terminate", release_terminate)
    workflow.add_node("release_dashboard_logging", release_dashboard_logging)
    workflow.add_node("release_create_summary", release_create_summary)
    workflow.add_node("release_update_upstream_ticket", release_update_upstream_ticket)
    workflow.add_node("release_send_confirmation", release_send_confirmation)
    workflow.add_node("release_finishing_node", release_finishing_node)

    # add edges
    workflow.add_edge(START, "release_proceed_without_verification")
    workflow.add_edge("release_proceed_without_verification", "release_router")
    workflow.add_conditional_edges(
        "release_router",
        lambda x: x.release_output_state.should_continue,
        {
            True: "release_payroll",
            False: "release_terminate"
        }
    )
    workflow.add_edge("release_payroll", "release_dashboard_logging")
    workflow.add_edge("release_dashboard_logging", "release_update_upstream_ticket")
    workflow.add_edge("release_update_upstream_ticket", "release_send_confirmation")
    workflow.add_edge("release_send_confirmation", "release_create_summary")
    workflow.add_edge("release_create_summary", "release_finishing_node")
    workflow.add_edge("release_finishing_node", END)
    workflow.add_edge("release_terminate", END)


    return workflow.compile()


# Main graph that combines all subgraphs
# This is the full end-to-end payroll processing graph
def create_graph() -> Graph:
    """Full end-to-end payroll processing graph with classification before account lookup."""
    workflow = StateGraph(PayrollState, output=PayrollState)

    # Add subgraphs
    workflow.add_node("classification__", create_classification_subgraph())
    workflow.add_node("company_worker_lookup__", create_company_worker_lookup_subgraph())
    workflow.add_node("payroll_processing__", create_payroll_processing_subgraph())
    workflow.add_node("validation__", create_validation_subgraph())
    workflow.add_node("execution__", create_execution_subgraph())
    workflow.add_node("release__", create_release_subgraph())

    # Start with classification
    workflow.add_edge(START, "classification__")

    # If classification should continue, proceed to account lookup
    workflow.add_conditional_edges("classification__",
        lambda s: s.classification_output_state.should_continue,
        {
            True:  "company_worker_lookup__",
            False: END,
        }
    )
    # if account lookup terminated early, go to END; otherwise go to payroll_processing__
    workflow.add_conditional_edges("company_worker_lookup__",
        lambda s: s.company_worker_lookup_output_state.should_continue,
        {
            True:  "payroll_processing__",
            False: END,
        }
    )
    # if payroll_extraction terminated early, go to END; otherwise go to validation__
    workflow.add_conditional_edges("payroll_processing__",
        lambda s: s.payroll_processing_output_state.should_continue,
        {
            True:  "validation__",
            False: END,
        }
    )
    # if payroll_extraction terminated early, go to END; otherwise go to validation__
    workflow.add_conditional_edges("validation__",
        lambda s: s.validation_output_state.should_continue,
        {
            True:  "execution__",
            False: END,
        }
    )
    # if execution terminated early, go to END; otherwise go to release__
    workflow.add_conditional_edges("execution__",
        lambda s: s.execution_output_state.should_continue,
        {
            True:  "release__",
            False: END,
        }
    )
    #workflow.add_edge("release", END)

    return workflow.compile()


# Main graph (used in playground/dev)
graph = create_graph()

# Named subgraphs (for testing or playground clarity)
account_lookup = create_company_worker_lookup_subgraph()
classification = create_classification_subgraph()
payroll_extraction = create_payroll_processing_subgraph()
validation = create_validation_subgraph()
execution = create_execution_subgraph()
release = create_release_subgraph()


# Subgraph entry points (for CLI, playground, or testing)
SUBGRAPHS = {
    "account_lookup": create_company_worker_lookup_subgraph,
    "classification": create_classification_subgraph,
    "payroll_extraction": create_payroll_processing_subgraph,
    "validation": create_validation_subgraph,
    "execution": create_execution_subgraph,
    "release": create_release_subgraph
    }
