import os
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, Union
import httpx
import logging
import json

logger = logging.getLogger(__name__)


class DashboardIntegration:
    """Integration with the dashboard API for logging PayrollState data"""

    def __init__(self):
        self.dashboard_url = os.getenv("DASHBOARD_API_URL", "http://api:8000")
        self.enabled = os.getenv("ENABLE_DASHBOARD_INTEGRATION", "false").lower() == "true"
        self.timeout = 10.0

    async def log_transaction(self, state: Union[Dict, Any], started_at: Optional[str] = None,
                              finished_at: Optional[str] = None) -> bool:
        """Log PayrollState to dashboard with timestamps"""

        if not self.enabled:
            logger.info("Dashboard integration disabled - set ENABLE_DASHBOARD_INTEGRATION=true")
            return False

        try:
            payload = self._build_dashboard_payload(state, started_at, finished_at)
            success = await self._send_to_dashboard(payload)

            if success:
                logger.info(f"✅ Successfully logged transaction {payload['ticket_id']} to dashboard")
            else:
                logger.error(f"❌ Failed to log transaction {payload['ticket_id']} to dashboard")

            return success

        except Exception as e:
            logger.error(f"❌ Dashboard integration error: {e}", exc_info=True)
            return False

    def _build_dashboard_payload(self, state: Union[Dict, Any], started_at: Optional[str],
                                 finished_at: Optional[str]) -> Dict[str, Any]:
        """Build payload from PayrollState with complete field mapping"""

        state_dict = self._safe_to_dict(state)

        input_state = state_dict.get("input_state", {})
        classification_state = state_dict.get("classification_output_state", {})
        account_state = state_dict.get("account_lookup_output_state", {})
        extraction_state = state_dict.get("payroll_extraction_output_state", {})
        validation_state = state_dict.get("validation_output_state", {})
        execution_state = state_dict.get("execution_output_state", {})
        release_state = state_dict.get("release_output_state", {})

        raw_ticket_id = input_state.get("IMSEPCID", "unknown")

        if raw_ticket_id and raw_ticket_id != "unknown":
            import hashlib
            ticket_hash = hashlib.md5(str(raw_ticket_id).encode()).hexdigest()
            ticket_uuid = f"{ticket_hash[:8]}-{ticket_hash[8:12]}-{ticket_hash[12:16]}-{ticket_hash[16:20]}-{ticket_hash[20:32]}"
        else:
            ticket_uuid = str(uuid.uuid4())

        start_time = started_at if started_at else datetime.utcnow().isoformat()
        end_time = finished_at if finished_at else datetime.utcnow().isoformat()

        start_ts = self._parse_timestamp(start_time)
        end_ts = self._parse_timestamp(end_time)
        processing_time_sec = self._calculate_processing_time(start_time, end_time)

        client_config = account_state.get("client_config", {})
        sender_metadata = account_state.get("sender_email_metadata", {})
        intent_triage = classification_state.get("intent_triage_metadata", {})
        l1_intent = classification_state.get("l1_intent_metadata", {})
        complexity_data = classification_state.get("complexity_metadata", {})

        payload = {
            "ticket_id": ticket_uuid,
            "session_id": f"session_{ticket_uuid}",
            "start_timestamp": start_ts.isoformat(),
            "end_timestamp": end_ts.isoformat(),
            "created_at": datetime.utcnow().isoformat(),
            "source": "agent",
            "payload": {
                "email_classification": self._extract_classification(classification_state),
                "easy_payroll_flag": self._extract_confidence_flag(classification_state),

                "payroll_entries": {
                    "success": self._extract_payroll_success(extraction_state)
                },
                "requires_customer_validation": not account_state.get("is_client_id_valid", True),
                "info_submitted_flex": bool(release_state.get("release_payroll", False)),
                "termination_dict": {
                    "termination_node": self._extract_termination_node(state_dict),
                    "termination_reason": self._extract_termination_reason(state_dict),
                    "termination_graph": "payroll_email_agent"
                },
                "model_version": "gpt-4o",
                "system_cost_usd": self._calculate_cost(classification_state),
                "processing_time_sec": processing_time_sec,
                "client_id": client_config.get("account_id", "unknown"),
                "client_name": client_config.get("account_name", "unknown"),
                "client_segment": self._extract_client_segment(client_config),
                "client_region": self._extract_client_region(sender_metadata),
                "complexity_level": self._extract_complexity_level(complexity_data),
                "ts_received": start_ts.isoformat(),
                "ts_flex_posted": end_ts.isoformat() if self._extract_payroll_success(extraction_state) else None,
                "pass_confidence_thresh": self._extract_confidence_flag(classification_state),
                "payroll_extracted": self._extract_payroll_success(extraction_state),
                "no_customer_validation": not account_state.get("is_client_id_valid", True),
                "stage_exited": self._extract_termination_node(state_dict),
                "reason_category": self._extract_reason_category(state_dict),
                "reason_description": self._extract_termination_reason(state_dict),
                "score_human": self._calculate_human_score(state_dict),
                "score_ground_truth": self._calculate_ground_truth_score(state_dict),
                "system_uptime_pct": 99.5,
                "intent_classification": self._extract_primary_intent(l1_intent),
                "intent_confidence": self._extract_intent_confidence(intent_triage),
                "employee_count": self._extract_employee_count(extraction_state),
                "total_amount": self._extract_total_amount(extraction_state),
                "email_subject": input_state.get("Subject", ""),
                "email_content": input_state.get("EmailContent", ""),
                "source_address": input_state.get("SourceAddress", ""),
                "destination_address": input_state.get("DestinationAddress", ""),
                "customer_id": input_state.get("CustomerID", ""),
                "contact_type": input_state.get("ContactType", ""),
                "original_ticket_id": raw_ticket_id,
                "full_payroll_state": state_dict
            }
        }

        return payload


    def _extract_complexity_level(self, complexity_data: dict) -> str:
        """Map complexity tag to standard levels"""
        try:
            complexity_tag = complexity_data.get("ComplexityTag", "medium")

            mapping = {
                "easy": "Easy",
                "medium": "Medium",
                "hard": "Complex",
                "complex": "Complex"
            }

            return mapping.get(str(complexity_tag).lower(), "Medium")
        except Exception:
            return "Medium"

    def _extract_client_segment(self, client_config: dict) -> str:
        """Extract client business segment"""
        try:
            account_name = client_config.get("account_name", "")

            if "corporation" in account_name.lower():
                return "Enterprise"
            elif "transportation" in account_name.lower():
                return "Transportation"
            elif "inc" in account_name.lower() or "llc" in account_name.lower():
                return "Small Business"
            else:
                return "General"
        except Exception:
            return "General"

    def _extract_client_region(self, sender_metadata: dict) -> str:
        """Extract client region from address"""
        try:
            address = sender_metadata.get("sender_address", "")

            if "NY" in address or "New York" in address:
                return "Northeast"
            elif "CA" in address or "California" in address:
                return "West"
            elif "TX" in address or "Texas" in address:
                return "South"
            elif "IL" in address or "Chicago" in address:
                return "Midwest"
            else:
                return "Unknown"
        except Exception:
            return "Unknown"

    def _extract_primary_intent(self, l1_intent: dict) -> str:
        """Extract primary intent from L1 classification"""
        try:
            max_score = 0
            primary_intent = "Unknown"

            for intent, score in l1_intent.items():
                try:
                    score_val = float(score) if score is not None else 0
                    if score_val > max_score:
                        max_score = score_val
                        primary_intent = intent
                except (ValueError, TypeError):
                    continue

            return primary_intent if max_score > 50 else "Unknown"
        except Exception:
            return "Unknown"

    def _extract_intent_confidence(self, intent_triage: dict) -> float:
        """Extract confidence score for intent classification"""
        try:
            is_about_payroll = intent_triage.get("isAboutPayroll", 0)
            return float(is_about_payroll) / 100.0 if is_about_payroll else 0.0
        except Exception:
            return 0.0

    def _extract_employee_count(self, extraction_state: dict) -> int:
        """Count number of employees processed"""
        try:
            extracted_payrolls = extraction_state.get("extracted_payrolls", {})
            payroll_entries = extracted_payrolls.get("payroll_entries", [])
            return len(payroll_entries) if payroll_entries else 0
        except Exception:
            return 0

    def _extract_total_amount(self, extraction_state: dict) -> float:
        """Calculate total payroll amount"""
        try:
            extracted_payrolls = extraction_state.get("extracted_payrolls", {})
            payroll_entries = extracted_payrolls.get("payroll_entries", [])

            total = 0.0
            for entry in payroll_entries:
                amount_str = entry.get("Amount", "0")
                amount_clean = amount_str.replace("$", "").replace(",", "")
                try:
                    total += float(amount_clean)
                except ValueError:
                    continue

            return total
        except Exception:
            return 0.0

    def _extract_reason_category(self, state_dict: dict) -> str:
        """Extract categorized failure reason"""
        try:
            termination_reason = self._extract_termination_reason(state_dict)

            if "validation" in termination_reason.lower():
                return "Validation Error"
            elif "account" in termination_reason.lower():
                return "Account Issue"
            elif "classification" in termination_reason.lower():
                return "Classification Error"
            elif "extraction" in termination_reason.lower():
                return "Extraction Error"
            elif "success" in termination_reason.lower():
                return "Success"
            else:
                return "Other"
        except Exception:
            return "Unknown"

    def _calculate_human_score(self, state_dict: dict) -> float:
        """Calculate simulated human evaluation score"""
        try:
            successful_flag = state_dict.get("successful_flag", False)
            if isinstance(successful_flag, dict):
                successful_flag = False

            if successful_flag:
                return 0.95
            else:
                return 0.65
        except Exception:
            return 0.75

    def _calculate_ground_truth_score(self, state_dict: dict) -> float:
        """Calculate ground truth evaluation score"""
        try:
            classification_state = state_dict.get("classification_output_state", {})
            intent_triage = classification_state.get("intent_triage_metadata", {})

            is_about_payroll = intent_triage.get("isAboutPayroll", 0)
            try:
                confidence = float(is_about_payroll) / 100.0 if is_about_payroll else 0.5
                return min(confidence, 0.98)  # Cap at 98%
            except (ValueError, TypeError):
                return 0.85
        except Exception:
            return 0.85

    def _safe_to_dict(self, obj):
        """Convert any object to a JSON-serializable dict"""
        try:
            if obj is None:
                return {}

            if isinstance(obj, dict):
                result = {}
                for key, value in obj.items():
                    result[key] = self._safe_to_dict(value)
                return result

            if isinstance(obj, list):
                return [self._safe_to_dict(item) for item in obj]

            if hasattr(obj, 'model_dump'):
                return self._safe_to_dict(obj.model_dump())
            elif hasattr(obj, 'dict'):
                return self._safe_to_dict(obj.dict())

            if isinstance(obj, (str, int, float, bool, type(None))):
                return obj

            if isinstance(obj, datetime):
                return obj.isoformat()

            if hasattr(obj, '__str__') and 'uuid' in str(type(obj)).lower():
                return str(obj)

            return str(obj)

        except Exception as e:
            logger.warning(f"Could not serialize object {type(obj)}: {e}")
            return str(obj) if obj is not None else {}

    def _extract_classification(self, classification_state: dict) -> str:
        """Extract classification for dashboard"""
        try:
            intent_metadata = classification_state.get("intent_triage_metadata", {})
            if not intent_metadata:
                return "Non-Payroll"

            is_about_payroll = intent_metadata.get("isAboutPayroll", 0)
            is_work_related = intent_metadata.get("isWorkRelated", 0)

            try:
                is_about_payroll = float(is_about_payroll) if is_about_payroll is not None else 0
                is_work_related = float(is_work_related) if is_work_related is not None else 0
            except (ValueError, TypeError):
                is_about_payroll = 0
                is_work_related = 0

            if is_about_payroll >= 80:
                return "Payroll"
            elif is_work_related >= 60:
                return "Work-Related"
            else:
                return "Non-Payroll"
        except Exception as e:
            logger.warning(f"Could not extract classification: {e}")
            return "Non-Payroll"

    def _extract_confidence_flag(self, classification_state: dict) -> bool:
        """Extract confidence flag for dashboard"""
        try:
            flag = classification_state.get("intent_triage_passes_flag", False)
            if isinstance(flag, bool):
                return flag
            elif isinstance(flag, dict):
                return False
            elif flag is None:
                return False
            else:
                return bool(flag)
        except Exception:
            return False

    def _extract_payroll_success(self, extraction_state: dict) -> bool:
        """Check payroll extraction success"""
        try:
            extracted_payrolls = extraction_state.get("extracted_payrolls", {})
            if not extracted_payrolls or not isinstance(extracted_payrolls, dict):
                return False

            success = extracted_payrolls.get("success", False)
            if isinstance(success, bool):
                return success
            elif isinstance(success, dict):
                return False
            elif success is None:
                return False
            else:
                return bool(success)
        except Exception:
            return False

    def _extract_termination_node(self, state_dict: dict) -> str:
        """Map termination status for dashboard"""
        try:
            successful_flag = state_dict.get("successful_flag", False)
            termination_flag = state_dict.get("termination_flag", False)

            if isinstance(successful_flag, dict):
                successful_flag = False
            if isinstance(termination_flag, dict):
                termination_flag = False

            successful_flag = bool(successful_flag) if successful_flag is not None else False
            termination_flag = bool(termination_flag) if termination_flag is not None else False

            if successful_flag:
                return "processed_ok"
            elif termination_flag:
                return "escalated"
            else:
                return "low_confidence"
        except Exception:
            return "low_confidence"

    def _extract_termination_reason(self, state_dict: dict) -> str:
        """Extract termination reason"""
        try:
            successful_flag = state_dict.get("successful_flag", False)
            if isinstance(successful_flag, dict):
                successful_flag = False
            successful_flag = bool(successful_flag) if successful_flag is not None else False

            if successful_flag:
                return "success"

            state_keys = [
                "classification_output_state",
                "account_lookup_output_state",
                "payroll_extraction_output_state",
                "validation_output_state",
                "execution_output_state",
                "release_output_state"
            ]

            for state_key in state_keys:
                state_obj = state_dict.get(state_key, {})
                if isinstance(state_obj, dict):
                    termination_reason = state_obj.get("termination_reason")
                    if termination_reason and isinstance(termination_reason, str):
                        return termination_reason

            return "unknown_error"
        except Exception:
            return "unknown_error"

    def _calculate_cost(self, classification_state: dict) -> float:
        """Calculate estimated cost based on complexity"""
        try:
            complexity_metadata = classification_state.get("complexity_metadata", {})
            if complexity_metadata:
                complexity = complexity_metadata.get("ComplexityTag", "medium")
            else:
                complexity = "medium"

            cost_map = {
                "easy": 0.005,
                "medium": 0.015,
                "hard": 0.030,
                "complex": 0.030
            }

            return cost_map.get(str(complexity).lower(), 0.015)
        except Exception:
            return 0.015

    def _calculate_processing_time(self, started_at: str, finished_at: str) -> int:
        """Calculate processing time in seconds"""
        try:
            start = self._parse_timestamp(started_at)
            end = self._parse_timestamp(finished_at)
            return int((end - start).total_seconds())
        except Exception:
            return 0

    def _parse_timestamp(self, timestamp_str: str) -> datetime:
        """Parse timestamp string"""
        if isinstance(timestamp_str, datetime):
            return timestamp_str

        timestamp_str = str(timestamp_str).replace("Z", "+00:00")

        try:
            return datetime.fromisoformat(timestamp_str)
        except Exception:
            return datetime.utcnow()

    async def _send_to_dashboard(self, payload: Dict[str, Any]) -> bool:
        """Send to dashboard API with better error handling"""
        try:
            json.dumps(payload, default=str)

            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.dashboard_url}/api/ingest",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                )

                if response.status_code == 202:
                    logger.debug(f"Dashboard API responded with 202 - payload accepted")
                    return True
                else:
                    logger.error(f"Dashboard API error: {response.status_code} - {response.text}")
                    return False

        except json.JSONEncoder as e:
            logger.error(f"JSON serialization failed: {e}")
            return False
        except httpx.TimeoutException:
            logger.error("Dashboard API timeout")
            return False
        except Exception as e:
            logger.error(f"Dashboard API request failed: {e}")
            return False


dashboard_integration = DashboardIntegration()
