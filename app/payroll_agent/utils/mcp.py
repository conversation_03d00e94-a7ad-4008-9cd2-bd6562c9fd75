from functools import lru_cache
from langchain_mcp_adapters.client import MultiServerMCPClient
from app.payroll_agent.config.config import settings
from app.cli.logging_utils import setup_logger


class MCPService:
    def __init__(self):
        self.logger = setup_logger(__name__)
        try:
            self.logger.info("Initializing MCP client")
            mcp_servers = settings.model_dump()['mcp_servers']
            self.logger.debug(f"MCP servers config: {mcp_servers}")
            self.client = MultiServerMCPClient(mcp_servers)
            self.logger.debug("MCP client initialized")
            # cache: { server_name: [StructuredTool, …], … }
            self.tools: dict[str, list] = {}
        except ConnectionError as e:
            self.logger.error(f"Failed to connect to MCP server: {type(e).__name__} - {e}", exc_info=True)
            raise

    async def get_tools_from_server(self, server_name: str):
        # return cached if available
        if server_name in self.tools:
            self.logger.debug(f"Returning cached tools for server '{server_name}'")
            return self.tools[server_name]

        # otherwise fetch, cache, and return
        try:
            self.logger.info(f"Fetching tools from MCP server '{server_name}'")
            tools = await self.client.get_tools(server_name=server_name)
            self.tools[server_name] = tools
            self.logger.debug(f"Cached {len(tools)} tools for server '{server_name}'")
            return tools
        except Exception as e:
            self.logger.error(f"Failed to get tools from MCP server '{server_name}': {type(e).__name__} - {e}", exc_info=True)
            raise

    async def call_mcp_tool(self, server_name: str, tool_name: str, tool_input: dict):
        try:
            self.logger.info(f"Calling MCP tool '{tool_name}' on server '{server_name}'")
            tools = await self.get_tools_from_server(server_name)
            tool = next(t for t in tools if t.name == tool_name)
            result = await tool.arun(tool_input)
            self.logger.debug(f"Tool '{tool_name}' result: {result}")
            return result
        except StopIteration:
            msg = f"Tool '{tool_name}' not found on server '{server_name}'"
            self.logger.error(msg)
            raise ValueError(msg)
        except Exception as e:
            self.logger.error(f"Failed to call MCP tool '{tool_name}' on server '{server_name}': {type(e).__name__} - {e}", exc_info=True)
            raise

    async def get_tool(self, server_name: str, tool_name: str):
        try:
            self.logger.info(f"Retrieving tool '{tool_name}' from server '{server_name}'")
            tools = await self.get_tools_from_server(server_name)
            tool = next(t for t in tools if t.name == tool_name)
            return tool
        except StopIteration as e:
            msg = f"Tool '{tool_name}' not found on server '{server_name}' : {type(e).__name__} - {e}"
            self.logger.error(msg, exc_info=True)
            raise ValueError(msg)
        except Exception as e:
            self.logger.error(f"Failed to retrieve MCP tool '{tool_name}': {type(e).__name__} - {e}", exc_info=True)
            raise


@lru_cache
def get_mcp_service():
    return MCPService()