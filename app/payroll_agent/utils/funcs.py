import yaml

from datetime import datetime
from pathlib import Path

from app.api.models.input import UpstreamModel
from app.cli.logging_utils import setup_logger
from app.payroll_agent.graph.states.common import PayrollState

logger = setup_logger(__name__)


def format_payroll_state_response(result_state):
    logger.info("Starting to format payroll state response")

    # suppose result_state is the AddableValuesDict you got back:
    state_map = dict(result_state)
    logger.debug(f"Converted result_state to dict: {state_map}")

    # Pydantic v2: use `model_validate` to parse a mapping into your model
    try:
        output_model = PayrollState.model_validate(state_map)
        logger.debug(f"Validated PayrollState model: {output_model}")
    except Exception as e:
        logger.error(f"Failed to validate PayrollState: {type(e).__name__} - {e}", exc_info=True)
        raise

    # now you can dump to a plain dict (recursively converts all nested BaseModels):
    plain_dict = output_model.model_dump()
    logger.debug(f"Dumped model to plain dict: {plain_dict}")

    # add completion timestamp, and ticket id
    started_at = plain_dict['input_state'].get('received_at')
    finished_at = datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
    ticket_id = plain_dict['input_state'].get('IMSEPCID')

    plain_dict['started_at'] = started_at
    plain_dict['finished_at'] = finished_at
    plain_dict['ticket_id'] = ticket_id
    logger.info(f"Timestamps and ticket added: started_at={started_at}, finished_at={finished_at}, ticket_id={ticket_id}")

    ## Add termination reason at level
    termination_dict = {}
    for key, value in plain_dict.items():
        logger.debug(f"Checking for termination in section '{key}': {value}")
        if isinstance(value, dict) and value.get('should_continue') is False:
            termination_dict['termination_graph'] = key
            termination_dict['termination_reason'] = value.get('termination_reason')
            termination_dict['termination_node'] = value.get('termination_node')
            logger.info(f"Termination detected in graph '{key}': reason={termination_dict['termination_reason']}, node={termination_dict['termination_node']}")
            break

    # add final flags
    termination_flag = bool(termination_dict.get("termination_node"))
    plain_dict['termination_flag'] = termination_flag
    plain_dict['successful_flag'] = not termination_flag

    if termination_dict:
        plain_dict['termination_dict'] = termination_dict
    else:
        logger.info("No termination detected in any subgraph")

    logger.debug("Finished formatting payroll state response")
    return plain_dict


def load_routers_config(name: str) -> dict:
    logger.debug("Loading routers configuration")
    try:
        # Build path to the YAML config file
        path = Path(__file__).resolve().parents[1] / "config" / f"{name}.yml"
        logger.debug(f"Resolved config path for '{name}': {path}")

        # Read and parse the YAML
        with open(path, "r") as f:
            config = yaml.safe_load(f)
            logger.debug(f"Successfully loaded config '{name}' ({len(config)} top-level keys)")
            return config

    except FileNotFoundError as e:
        logger.error(f"Config file not found for '{name}' at {path}: {type(e).__name__} - {e}", exc_info=True)
        raise
    except yaml.YAMLError as ye:
        logger.error(f"Error parsing YAML for '{name}': {ye}: {type(ye).__name__} - {ye}", exc_info=True)
        raise
    except Exception as e:
        logger.error(f"Unexpected error loading config '{name}': {e}: {type(e).__name__} - {e}", exc_info=True)
        raise


def parse_upstream_model(upstream_model: UpstreamModel):
    """
    Parse the upstream model dictionary into a PayrollState instance.
    This is used to convert the input model from the API into the state format used by the graph.
    """
    model_dict = dict()
    logger.debug("Parsing upstream model...")
    try:
        model_dict['companyID'] = upstream_model.companyID
        model_dict['uid'] = upstream_model.comment.uid
        model_dict['id'] = upstream_model.comment.id
        model_dict['CustomerDomain'] = upstream_model.comment.user_properties.get('string:Sender Domain', '')
        model_dict['EmailContent'] = upstream_model.comment.messages[0].body.text
        model_dict['SourceAddress'] = upstream_model.comment.user_properties.get('string:Sender', '')
        model_dict['DestinationAddress'] = upstream_model.comment.messages[0].to
        model_dict['Subject'] = upstream_model.comment.messages[0].subject.text
        model_dict['Payload'] = upstream_model.model_dump(by_alias=True)
        model_dict['received_at'] = upstream_model.received_at

        return model_dict
    except Exception as e:
        logger.error(f"Failed to parse upstream model: {type(e).__name__} - {e}", exc_info=True)
        raise