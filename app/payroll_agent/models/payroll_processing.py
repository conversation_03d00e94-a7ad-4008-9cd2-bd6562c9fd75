from typing import Optional, List, Union
from pydantic import BaseModel, Field


class PayAmountCommand(BaseModel):
    payAmount: float = Field(..., description="Straight monetary value to pay")

class PayHoursCommand(BaseModel):
    payHours: float = Field(..., description="Number of hours to pay")
    payRate: Optional[float] = Field(None, description="Override rate for this pay")
    payRateId: Optional[str] = Field(None, description="Predefined rate ID for this pay")

class PayUnitsCommand(BaseModel):
    payUnits: float = Field(..., description="Number of units to pay")
    payRate: Optional[float] = Field(None, description="Override rate for this pay")
    payRateId: Optional[str] = Field(None, description="Predefined rate ID for this pay")

class EmployeeCommand(BaseModel):
    name : str = Field(..., description="name of the employee as it appears in the email")
    command: Union[PayAmountCommand, PayHoursCommand, PayUnitsCommand]

class Payroll(BaseModel):
    commands: List[EmployeeCommand] = Field(default_factory=list)


## LEGACY PAYROLL ENTRIES MODEL
class PayrollEntries(BaseModel):
    payroll_entries: List = Field(default_factory=list, description="payroll_entries")
    success: bool = Field(default=None, description="success")
    error: Optional[str] = Field(None, description="error")



