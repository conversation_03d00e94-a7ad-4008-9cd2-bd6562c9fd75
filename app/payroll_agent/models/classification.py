from pydantic import BaseModel, Field

from typing import Literal, Optional, List

# Layer 0: Initial triage to determine if an email is relevant, actionable, and payroll-related
class IntentTriage(BaseModel):
    isWorkRelated: int = Field(..., description="Likelihood the email is work-related (not spam, promo, or unrelated).")
    RequiresWork: int = Field(..., description="Likelihood that the email requires any action from a Paychex employee.")
    isAboutPayroll: int = Field(..., description="Likelihood that the email involves entering, updating, or submitting payroll-related information — including approvals, edits, or follow-ups.")
    isOnlyAboutPayroll: int = Field(..., description="Likelihood that the email contains a single, clear request related to payroll entry, with no other topics, questions, or additional tasks included.")

# Layer 1: Deeper intent classification assuming the message is relevant to payroll
class L1Intents(BaseModel):
    EnterPayroll: int = Field(..., description="Customer is providing pay details for entry.")
    RunPayroll: int = Field(..., description="Customer is requesting payroll to be processed or approved.")
    ChangeEmployeeStatus: int = Field(..., description="Customer wants to add, remove, or change employee status.")
    SystemQuestion: int = Field(..., description="Customer has a question or issue about the Paychex system.")
    FixMistake: int = Field(..., description="Customer wants to correct a mistake from a previous payroll.")
    AdjustWithholdings: int = Field(..., description="Customer requests changes to withholdings or benefits.")
    OtherPayrollRelated: int = Field(..., description="Request is payroll-related but doesn’t match other categories.")
    NotPayrollRelated: int = Field(..., description="Email is not related to payroll (e.g., benefits, HR, unrelated topic).")
    NotAbleToClassifyIntent: int = Field(..., description="Email is too ambiguous or vague to classify.")

class RequestTypeTags(BaseModel):
    EmployeeCount: Literal["1_employee", "multiple_employees", "unknown"] = Field(..., description="Indicates whether the request is about a single employee or multiple employees.")
    LineItemsPerPerson: Literal["1", "2", "3", "4_or_more", "unknown"] = Field(..., description="Number of distinct pay items (e.g., base, bonus, tips) provided per employee.")
    PayType: Literal["salary", "hourly_plus_hours", "other_non_standard", "other", "unknown"] = Field(..., description="The pay structure described in the request — salary, hourly + hours, or other.")
    Segmentation: Literal["none", "by_location", "by_role", "by_company", "other", "unknown"] = Field(..., description="Whether the data is segmented by team, region, role, company, or not at all.")
    OrganizationOfComponents: Literal["name_level", "component_level", "other", "unknown"] = Field(..., description="Describes how pay components are organized — under each employee, by component type, or other.")
    PayPeriodUnit: Literal["day", "week", "two_weeks", "month", "other", "unknown"] = Field(..., description="The time unit used for the pay period mentioned in the request.")
    WeekendsIncluded: Literal["exclude", "include", "unknown"] = Field(..., description="Indicates if the request includes weekends in the date range.")
    NumPayPeriods: Literal["1", "multiple","unknown"] = Field(..., description="Specifies whether the request covers a single pay period or more than one.")
    ReferenceToPrior: Literal["none", "reference","unknown"] = Field(..., description="Whether the customer refers to a previous payroll or message as context.")
    SpecialInstructions: Literal["none", "unaddressable_by_agent","unknown"] = Field(..., description="Notes or requests that are ambiguous, vague, or cannot be acted on by an agent.")
    RequiresCalculation: Literal["none", "add_subtract", "multiply_divide", "multi_step", "other", "unknown"] = Field(..., description="Whether the request involves any calculations and how complex they are.")
    Sentiment: Literal["neutral", "positive", "negative", "unknown"] = Field(..., description="The overall tone or sentiment of the customer’s message.")
    HasAttachments: Literal["yes", "no", "unknown"] = Field(..., description="Whether the email includes any attachments.")
    AttachmentType: Literal["none", "excel", "pdf", "other", "unknown"] = Field(..., description="Type of file attached, if any.")
    CallRequested: Literal["yes", "no", "unclear"] = Field(..., description="Customer explicitly asks to be called.")
    InsufficientInformation: Literal["yes", "no"] = Field(..., description="Whether the request lacks enough information to proceed.")

class ComplexityTags(BaseModel):
    ComplexityTag: str = Field(..., description="Complexity level: easy, medium, or hard")
    ComplexityTagReason: List[str] = Field(..., description="List of reasons that contributed to the assigned complexity")
