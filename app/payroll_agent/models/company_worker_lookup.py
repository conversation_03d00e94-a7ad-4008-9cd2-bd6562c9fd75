from typing import Dict, List, Optional
from pydantic import BaseModel, Field


class SenderEmailMetadata(BaseModel):
    """Key details that appear in an automated payroll-notification email."""
    sender_name: Optional[str] = Field(default='', description="Human-readable name, e.g. Acme Payroll Bot")
    company_name: Optional[str] = Field(default='', description="Company issuing the email")
    sender_phone_number: Optional[str] = Field(default='', description="Sender's phone number")
    sender_address: Optional[str] = Field(default='', description="Sender's address")
    employees_names: list[str] = Field(default=[], description="List of employees names present in the email")

class ValidateSenderMetadata(BaseModel):
    """Key details that appear in an automated payroll-notification email."""
    valid: bool = Field(default=False, description="Whether the sender's info is validated")
    errors : List[Dict[str, str]] = Field(default_factory=list, description="List of any errors found")


class Worker(BaseModel):
    name: str = Field(..., description="Name of the worker mentioned in the email")
    worker_number: Optional[int] = Field(None, description="Worker number")
    confidence: int = Field(..., description="Confidence level of the worker")


class WorkersMatch(BaseModel):
    workers: Optional[List[Worker]] = Field(default_factory=list, description="List of workers mentioned in email")
