# Payroll Email Agent

A LangGraph-based email agent for processing payroll-related emails using FastAPI and OpenAI.

## Project structure
This project is organized to support modular, testable, and scalable LangGraph-based agents. Below is a breakdown of the key directories and files.

```bash
app/
├── main.py                               # Entrypoint: runs LangGraph or starts FastAPI server
│
├── api/                                  # FastAPI routing and request/response models
│   ├── routes.py                         # Defines public API endpoints
│   └── models.py                         # Pydantic models for external request/response payloads
│
├── cli/                                  # Command-line interface
│   ├── logging_utils.py                  # colorized logger settings 
│
├── payroll_agent/                        # Core logic for the payroll agent
│   ├── config/                           # Placeholder for configuration files
│   │   ├── config.py                     # Loads config values from .env or YAML
│   │   └── router_config.py              # Threshold values for the router nodes
|
│   ├── graph/                            # LangGraph graph builder and shared state definitions
│   │   ├── graph_builder.py              # Wires up LangGraph nodes and edges
│   │   ├── states/                       # Pydantic models for the subgraphes 
│   │   │   └── company_worker_lookup.py         # Pydantic models for the account_lookup graph
│   │   │   └── classification            # Pydantic models for the classification graph
│   │   │   └── common.py                 # Pydantic models for the parent graph
│   │   │   └── payroll_processing.py     # Pydantic models for the payroll extraction graph
│   │   │   └── validation.py             # Pydantic models for the validation graph
│   │   │   └── execution.py              # Pydantic models for the execution graph
│   │   │   └── release.py                # Pydantic models for the release graph
│
│   ├── models/                           # LLM output schemas used for structured parsing
│   │   └── company_worker_lookup.py             # Pydantic schemas for structured outputs from LLMs in account_lookup graph
│   │   └── classification.py             # Pydantic schemas for structured outputs from LLMs in classification graph
│   │   ├── payroll_processing.py         # Pydantic schemas for extracted payroll entries with LLMs
│   │   └── validation.py                 # Pydantic schemas for validation of extracted entries
│   │   └── execution.py                  # Placeholder for future schemas in execution graph
│   │   └── release.py                    # Placeholder for future schemas in execution graph
│
│   ├── nodes/                            # LangGraph-compatible node functions (SubGraphs)
│   │   ├── company_worker_lookup.py             # Extract sender and client info from raw email
│   │   ├── classification.py             # Classify the type of payroll task
│   │   ├── payroll_processing.py         # Extract, validate payroll entries with LLMs
│   │   ├── validation.py                 # Validate payrolls
│   │   ├── execution.py                  # Create payrolls
│   │   └── release.py                    # Release payrolls
│
│   ├── prompts/                          # YAML-based prompt templates used by each node
│   │   ├── company_worker_lookup.yml            # Prompts for nodes/company_worker_lookup.py
│   │   └── payroll_processing.yml        # Prompt for nodes/payroll_processing.py
│   │   └── classification.py             # Prompt for nodes/classification.py
│   │   └── validation.py                 # Prompt for nodes/validation.py
│   │   └── release.py                    # Prompt for nodes/validation.py
│ 
│   ├── evaluation/                       # Evaluation scripts and cases
│   │   ├── evaluation_cases/             # Cases for testing
│   │   └── run_evaluation.py             # Script to run evaluation for relevant cases in evaluation_cases/
│   │   └── run_single_evaluation.py      # Notebook to run single evaluation test for debugging
│
│   └── utils/                            # Shared helpers and infrastructure
│       ├── funcs.py                      # Utility funcs 
│       └── prompt.py                     # Loads prompts from YAML based on node name
│       └── mock_services.py              # Mock connections to APIs
│       └── mcp.py                        # MCP utility functions
```

### 📌 Notes:
- **LangGraph** handles the node orchestration and state transitions.
- Each node uses its own **structured prompt**, stored in YAML and loaded dynamically.
- The agent is built for **clean separation of reasoning, validation, and submission logic**.


## Pre-requisites
Before testing the app, you need to launch the *MCP Paychex Flex Server*, which is used across the app.
To do so, navigate to the repo [payroll-mcp-servers](https://github.com/paychex/payroll-mcp-servers) and follow the instructions in `README.md` to clone the repo locally and run the server using *Docker Compose*.

Make sure to set the environment variables in the `.env.example` in a `.env` file. For the payroll agent, the relevant variables for authentication are:
```
MCP_SERVERS__PAYCHEX__CLIENT_ID="payroll_client"
MCP_SERVERS__PAYCHEX__CLIENT_SECRET="default_secret"
```

**IMPORTANT:** These values must match the credentials configured in the MCP server (`DEFAULT_CLIENT_ID` and `DEFAULT_CLIENT_SECRET`). If the client ID or secret do not match, authentication will fail and the agent will not be able to communicate with the MCP server.

### OAuth Configuration

The OAuth settings for the payroll agent are managed through environment variables in your `.env` file:

> **⚠️ WARNING:** Do not change or add these values to your `.env` unless absolutely necessary. The default values are required for the agent to communicate with the default MCP server setup. Only modify or add them if you know you need to use different credentials, and be sure to update the MCP server configuration to match.

```bash
# MCP Server Credentials for OAuth Authentication
MCP_SERVERS__PAYCHEX__CLIENT_ID="payroll_client"
MCP_SERVERS__PAYCHEX__CLIENT_SECRET="default_secret"
```
- These must match the server's `DEFAULT_CLIENT_ID` and `DEFAULT_CLIENT_SECRET`.
- If you override these values in your `.env`, ensure the client ID and secret are identical in both the payroll agent and the MCP server configuration.
- Mismatched values will result in authentication failures.

---

### Docker Networking

All MCP services and the Payroll Email Agent run on a shared Docker network that is automatically created by Docker Compose. This allows seamless communication between the Payroll Email Agent, the Paychex Payroll MCP server, the Upstream server, and any other services you add. No manual network setup is required—Docker Compose will handle network creation and service discovery for you.

The MCP server will run on your localhost at port 9000, and needs to be running as long as the *Payroll Email Agent* is being used. 

Once that’s done, you’re ready to proceed with testing the app.

## Setup

1. Clone the repository:
```bash
<NAME_EMAIL>:paychex/payroll-email-agent.git
cd payroll-email-agent
```

2. Install uv (if not already installed):
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

3. Create and activate virtual environment:
```bash
uv venv
source .venv/bin/activate  # On Unix/macOS
# or
.venv\Scripts\activate  # On Windows
```

4. Install dependencies:
```bash
uv pip install -e .
```

5. Install dev & test extras (linting, tests, pre-commit)
```bash
uv pip install -e ".[dev,test]"
```

6. Install the Git pre-commit hook (runs Ruff auto-fix on every commit)
```bash
pre-commit install
```

7. Copy the environment template and fill in your values:
```bash
cp .env.example .env
```

8. Edit `.env` with your configuration values, make sure the MCP server is running at `http://localhost:9000/`, the `MCP_SERVERS__PAYCHEX__CLIENT_ID` is set and equal to the `DEFAULT_CLIENT_ID` and  `MCP_SERVERS__PAYCHEX__CLIENT_SECRET` is set and equal to the `DEFAULT_CLIENT_SECRET` in the MCO repo.

    #### Update LANGCHAIN_API_KEY

    To get started with LangSmith, you need to create an account.
    - You can sign up for free [here](https://smith.langchain.com/?_gl=1*1xveyww*_ga******************************_ga_47WX3HKKY2*czE3NDY2MzEwODUkbzEkZzAkdDE3NDY2MzEwODUkajAkbDAkaDA.).
    - Login with your account.
    - You will receive a confirmation email.
    - Take the token and save it in a vault key of your choice.
    - Paste the key into your .env file and save it.

    #### Update OPENAI_API_KEY

    - Go to your OpenAI platform (also known as API platform) and log-in with your account and follow along the SSO authentication steps.
    - Hit the gear icon (Settings) on the right-hand side corner
    - Go to `API Keys` tab
    - Click on `Create new secret key` button providing a name for that key and the project.
    - Grant the level of permissions (All, Restricted or Read only) and hit `Create secret key`.
    - Take the token and save it in a vault key of your choice.
    - Paste the key into your .env file and save it.

## Running the Service
### (without docker)

Start the FastAPI server:
```bash
uvicorn app.main:app --reload
```
The API will be available at `http://localhost:8000`

## Running with Docker Compose
**Prerequisites:** Docker & Docker Compose installed on your machine.

1. **Ensure your `.env` is in place (see Setup step 5).**

2. **Build & start the service:**
   ```bash
   docker-compose up --build
   ```
    This will start two services:
   * FastAPI application on port 8000
   * LangGraph Playground on port 2024

3. **Access the services:**
   * API: `http://localhost:8000` 
     * Swagger UI: `http://localhost:8000/docs`
     * ReDoc: `http://localhost:8000/redoc`
   * LangGraph Playground: `https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024`

4. **Features:**
   * Mounts your local code for live reload
   * Preserves the virtual environment inside the container
   * Automatically detects code changes and restarts the FastAPI server
   * Provides a visual interface for exploring and debugging your LangGraph workflow

5. **Custom port configuration (optional):**
   ```bash
   PAYROLL_AGENT_PORT=9000 docker-compose up --build
   ```

## API Documentation

Once the server is running, visit:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

### LangGraph Playground
```bash
uvx --refresh --from "langgraph-cli[inmem]" --with-editable . --python 3.11 langgraph dev
```
## Running Evaluation Tests

You can validate the full email processing pipeline (input → graph → output) using the provided evaluation script.

### Standard Evaluation

This script runs all test cases in a single pass:

- Load test cases from `evaluation_cases/`
- Send them to the running FastAPI endpoint
- Save:
  - Full results to `evaluation_results/evaluation_results_*.json`
  - Flattened results to `evaluation_results/evaluation_results_*.csv`

### Batch Evaluation (High-Volume Test Runner)

This script is designed for running **large-scale evaluation** in manageable batches, with built-in support for resuming if interrupted.

- Loads input JSON files from: app/payroll_agent/evaluation/evaluation_cases/batch_cases/
- Sends test cases to the FastAPI endpoint
- Saves:
    - .xlsx results per batch to `app/payroll_agent/evaluation/evaluation_batch_results/`
    - Progress to checkpoint.json to allow resuming after interruption
    -  **Each batch is saved immediately after it completes**, so results are not lost even if the process is interrupted.


Each batch result includes:
- A file like evaluation_batch_0.xlsx

### Run Locally

1. Make sure that the service is running at `http://localhost:8000/`. 
2. In a new terminal, run the script:
   - Standard Evaluation: 
      ```
      python app/payroll_agent/evaluation/run_evaluation.py
      ```
   - Batch run: 
      ```
      python app/payroll_agent/evaluation/run_batch_evaluation.py
      ```

### Run from Docker

If you're using Docker:

1. Start the services (if not already running):
   ```
   docker-compose up --build
   ```
2. Then in another terminal, run:
   ```
   docker ps -a
   ```

   Example output:
   ```
   (payroll-email-agent) (.venv) (base) DENMAC-51099:payroll-email-agent 51099$ docker ps -a
   CONTAINER ID   IMAGE                                      COMMAND                  CREATED          STATUS          PORTS                              NAMES
   10593bf49920   payroll-email-agent_payroll-agent          "python -m uvicorn a…"   33 seconds ago   Up 31 seconds   0.0.0.0:8000->8000/tcp             payroll-email-agent_payroll-agent_1
   9baaa8a08c46   payroll-email-agent_langgraph-playground   "uvx --refresh --fro…"   33 seconds ago   Up 31 seconds   0.0.0.0:2024->2024/tcp, 8000/tcp   payroll-email-agent_langgraph-playground_1
   ```
3. Run the evaluation command with the container id
   - Standard Evaluation: 
      ```
      docker exec -it [CONTAINRER ID] python app/payroll_agent/evaluation/run_evaluation.py
      ```
   - Batch run: 
      ```
      docker exec -it [CONTAINRER ID] python app/payroll_agent/evaluation/run_batch_evaluation.py
      ```



## Development

- Uses `ruff` for linting
- Uses `pytest` for testing
- Follows Python 3.9+ standards 

## Additional Documentation

More detailed documentation can be found in the `docs/` folder.

For example:
- [`docs/LangGraph-playground.md`](docs/testing_langgraph_playground.md) explains how to test both the full graph and individual subgraphs in LangGraph Studio.
- [`docs/LangSmith.md`](docs/LangSmith.md) provides information on using the LangSmith dashboard for monitoring and debugging your LangGraph applications.