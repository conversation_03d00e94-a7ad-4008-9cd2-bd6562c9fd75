# Docs for the Azure Web Apps Deploy action: https://github.com/azure/functions-action
# More GitHub Actions for Azure: https://github.com/Azure/actions
# More info on Python, GitHub Actions, and Azure App Service: https://aka.ms/python-webapps-actions

name: CD - Payroll Email Agent (dev)

on:
  push:
    branches: [dev]
  workflow_dispatch:
    inputs:
      ref:
        description: 'Git ref for the SHA you want (defaults to dev HEAD)'
        required: false
        default: dev

env:
  # Resource naming follows Azure best practices: service-purpose-region-environment-instance
  AZURE_RESOURCE_GROUP: rg-emailpayrollautomation-eastus-sb-001     # Resource group for payroll AI in East US, sandbox env, instance 001
  AZURE_CONTAINER_APP: ca-payroll-ai-agent-sb-001                    # Container app for payroll agent, sandbox env, instance 001
  AZURE_REGISTRY_LOGIN_SERVER: acrpayrollaiagentsb001.azurecr.io    # Container registry (shortened to meet 24-char limit)
  AZURE_IMAGE_REPO: paychex/payroll-ai-agent             # Repository name within registry
  AZURE_REGISTRY_USERNAME: acrpayrollaiagentsb001   # ACR username
  AZURE_REGISTRY_PASSWORD: ${{ secrets.AZURE_REGISTRY_PASSWORD }}   # ACR password
  AZURE_SUBSCRIPTION_ID: ${{ secrets.AZURE_SUBSCRIPTION_ID }} # Azure subscription ID
  KEY_VAULT_NAME: kv-payrollaiagent-sb-001                  # Key vault for payroll AI, sandbox env, instance 001
  DOCKER_DEFAULT_PLATFORM: linux/amd64                   # Ensure consistent builds across platforms
  MCP_SERVERS__PAYCHEX__URL: https://ca-mcp-server-sb-001.ambitiousisland-4978aff0.eastus.azurecontainerapps.io/mcp/
  MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL: https://ca-mcp-server-sb-001.ambitiousisland-4978aff0.eastus.azurecontainerapps.io/auth/token
  MCP_SERVERS__PAYCHEX__CLIENT_ID: test
  MCP_SERVERS__PAYCHEX__CLIENT_SECRET: test
  
jobs:
  build-and-deploy:
    runs-on: [self-hosted, nonprod] # TODO: Once new runner exists we need to update here
    environment:
      name: dev
      url: ${{ steps.deploy-to-aca.outputs.container-app-url }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Log in to Azure Container Registry
        uses: azure/docker-login@v1
        with:
          login-server: ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}
          username: ${{ env.AZURE_REGISTRY_USERNAME }}
          password: ${{ env.AZURE_REGISTRY_PASSWORD }}

      - name: Build and push Docker image
        run: |
          echo "Building docker image from commit: ${{ github.sha }}"
          docker build -f Dockerfile -t ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:dev.${{ github.sha }} .
          docker build -f Dockerfile -t ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:dev.latest .
          echo "Pushing images to ACR..."
          docker push ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:dev.${{ github.sha }}
          docker push ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:dev.latest
          echo "Successfully pushed images to ACR"

      - name: Deploy to Azure Container App
        id: deploy-to-aca
        uses: azure/CLI@v1
        with:
          inlineScript: |
            az login --identity
            az account set --subscription ${{ env.AZURE_SUBSCRIPTION_ID }}

            # TODO: Uncomment when Key Vault RBAC permissions are configured
            # echo "Updating secrets for Container App ${{ env.AZURE_CONTAINER_APP }}"
            # az containerapp secret set \
            #   -g ${{ env.AZURE_RESOURCE_GROUP }} \
            #   -n ${{ env.AZURE_CONTAINER_APP }} \
            #   --secrets \
            #     openai-key=keyvaultref:https://${{ env.KEY_VAULT_NAME }}.vault.azure.net/secrets/OPENAI-API-KEY,identityref:system \
            #     langchain-api-key=keyvaultref:https://${{ env.KEY_VAULT_NAME }}.vault.azure.net/secrets/LANGCHAIN-API-KEY,identityref:system

            IMAGE_TAG="dev.${{ github.sha }}"
            FULL_IMAGE_NAME="${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:$IMAGE_TAG"

            echo "Deploying image: $FULL_IMAGE_NAME to ${{ env.AZURE_CONTAINER_APP }}"


            # TODO: Once keyvault is working we need to add back in line 94. This should reference the keyvault secrets not the github secrets as it is now
            # OPENAI_API_KEY=secretref:openai_key \
            # LANGCHAIN_API_KEY=secretref:langchain_api_key \
            az containerapp update \
              --subscription ${{ env.AZURE_SUBSCRIPTION_ID }} \
              -g ${{ env.AZURE_RESOURCE_GROUP }} \
              -n ${{ env.AZURE_CONTAINER_APP }} \
              --image $FULL_IMAGE_NAME \
              --workload-profile-name consumption \
              --cpu 4 --memory 8Gi \
              --replace-env-vars \
                 OPENAI_API_KEY="${{ secrets.OPENAI_API_KEY }}" \
                 LANGCHAIN_API_KEY="${{ secrets.LANGCHAIN_API_KEY }}" \
                 LANGCHAIN_TRACING_V2=true \
                 LANGCHAIN_ENDPOINT=https://api.smith.langchain.com \
                 LANGCHAIN_PROJECT=payroll-email-agent \
                 MCP_SERVERS__PAYCHEX__URL="${{env.MCP_SERVERS__PAYCHEX__URL}}" \
                 MCP_SERVERS__PAYCHEX__TRANSPORT=streamable_http \
                 MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL="${{env.MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL}}" \
                 MCP_SERVERS__PAYCHEX__CLIENT_ID="${{env.MCP_SERVERS__PAYCHEX__CLIENT_ID}}" \
                 MCP_SERVERS__PAYCHEX__CLIENT_SECRET="${{env.MCP_SERVERS__PAYCHEX__CLIENT_SECRET}}"

            APP_URL=$(az containerapp show -g ${{ env.AZURE_RESOURCE_GROUP }} -n ${{ env.AZURE_CONTAINER_APP }} --query properties.configuration.ingress.fqdn -o tsv)
            echo "container-app-url=https://$APP_URL" >> $GITHUB_OUTPUT

      # Clean up
      - name: Clean up Docker images
        if: always()
        run: |
          docker rmi ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:dev.${{ github.sha }} || true
          docker rmi ${{ env.AZURE_REGISTRY_LOGIN_SERVER }}/${{ env.AZURE_IMAGE_REPO }}:dev.latest || true
