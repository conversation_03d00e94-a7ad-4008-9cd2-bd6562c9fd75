name: CI - Payroll Email Agent (dev)

on:
  push:
    branches: [ "dev" ]
  pull_request:
    branches: [ "dev" ]
    paths:   [ "**" ]
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.ref }}
  cancel-in-progress: true

env:
  DOCKER_DEFAULT_PLATFORM: linux/amd64             # Ensure consistent builds across platforms

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    # runs-on: [ self-hosted, aca-gh-runner ] # Uncomment for self-hosted runner

    permissions:
      contents: read
      pull-requests: write

    steps:
      - uses: actions/checkout@v4

      - name: Build test image
        run: docker build -f Dockerfile -t app-test:${{ github.sha }} .

      # - name: Install test dependencies & run tests
      #   run: |
      #     docker run --entrypoint "" app-test:${{ github.sha }} \
      #                sh -c "uv pip install -e '.[dev,test]'"
      #     CID=$(docker ps -a -q | head -1)
      #     docker commit $CID app-test:${{ github.sha }}
      #     docker rm $CID

      #     docker run --entrypoint "" app-test:${{ github.sha }} sh -c "uv run ruff format src/ --verbose"
      #     docker run --entrypoint "" app-test:${{ github.sha }} sh -c "uv run ruff check src/"
      #     docker run --entrypoint "" app-test:${{ github.sha }} sh -c "cp .env.example .env && uv run pytest"
      #     docker run --entrypoint "" app-test:${{ github.sha }} sh -c "cp .env.example .env && uv run pytest --cov-report term --cov=src"
          
      #     CID=$(docker ps -a -q | head -1)
      #     docker cp $CID:/app/coverage.xml ./coverage.xml
      #     docker cp $CID:/app/coverage.txt ./coverage.txt
      #     docker rm $CID # Clean up the test container

      # - name: PR coverage comment
      #   if: github.event_name == 'pull_request'
      #   uses: MishaKav/pytest-coverage-comment@dev
      #   with:
      #     pytest-coverage-path: ./coverage.txt
      #     pytest-xml-coverage-path: ./coverage.xml

      # - name: Upload coverage reports
      #   if: github.event_name == 'pull_request'
      #   uses: actions/upload-artifact@v4
      #   with:
      #     name: coverage-reports
      #     path: |
      #       coverage.xml
      #       coverage.txt

      # Clean up
      - name: Clean up Docker images
        if: always()
        run: |
          docker rmi app-test:${{ github.sha }} || true
