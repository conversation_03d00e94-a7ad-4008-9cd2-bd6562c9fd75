SUB="sub-paychexai-sandbox-002"           
RG="rg-emailpayrollautomation-eastus-sb-001"
LOC="eastus"
ACR="acrpayrollaiagentsb001"               
ACA_ENV="aca-payrollai-sb-001"             
KV="kv-payrollaiagent-sb-001"


# per repo
APP="ca-payroll-ai-agent-sb-001"

# per repo
IMAGE_REPO="paychex/payroll-ai-agent"

# network
VNET_NAME="vnet-payrollai-sb-001"
VNET_ADDRESS_PREFIX="********/26"
ACA_SUBNET_NAME="snet-payrollaiagent-sb-001"
ACA_SUBNET_PREFIX="********/27"

# artifacts
az acr create -n $ACR -g $RG -l $LOC --sku Premium
az keyvault create -n $KV -g $RG -l $LOC


# Create VNet and Subnet for ACA Environment
az network vnet create \
  --name $VNET_NAME \
  --resource-group $RG \
  --location $LOC \
  --address-prefix $VNET_ADDRESS_PREFIX

az network vnet subnet create \
  --name $ACA_SUBNET_NAME \
  --resource-group $RG \
  --vnet-name $VNET_NAME \
  --address-prefix $ACA_SUBNET_PREFIX \
  --delegations 'Microsoft.App/environments'

ACA_SUBNET_ID=$(az network vnet subnet show --resource-group $RG --vnet-name $VNET_NAME --name $ACA_SUBNET_NAME --query id -o tsv)


az containerapp env create -n $ACA_ENV -g $RG -l $LOC \
  --infrastructure-subnet-resource-id $ACA_SUBNET_ID \
  --internal-only false


# /*pending from here*/

PRINCIPAL=$(az containerapp env show -n $ACA_ENV -g $RG --query identity.principalId -o tsv)

# container app env
az keyvault set-policy -n $KV --secret-permissions get list --object-id $PRINCIPAL


# per repo / container app
# this is optional since this would be done in the CD pipeline
az containerapp create -n $APP -g $RG --environment $ACA_ENV \
  --image $ACR_NAME.azurecr.io/$IMAGE_REPO:latest \
  --registry-server $ACR_NAME.azurecr.io \
  --registry-username $ACR_NAME \
  --registry-password $(az acr credential show --name $ACR_NAME --query "passwords[0].value" -o tsv) \
  --ingress external --target-port 8000 \
  --cpu 4 --memory 8Gi


 # /*pending until here*/

# Define a name for the User-Assigned Managed Identity
UAMI_NAME="uai-payrollaiagent-dev"

# Create the User-Assigned Managed Identity in the resource group
az identity create --name $UAMI_NAME --resource-group $RG --location $LOC

# Get the IDs needed for role assignment and GitHub secrets
UAMI_PRINCIPAL_ID=$(az identity show --name $UAMI_NAME --resource-group $RG --query 'principalId' -o tsv)
UAMI_CLIENT_ID=$(az identity show --name $UAMI_NAME --resource-group $RG --query 'clientId' -o tsv)
CURRENT_SUB_ID=$(az account show --query id -o tsv)

# this cannot be run because require tenant level permissions
# az ad sp create --id $APP_ID

SCOPE="/subscriptions/$CURRENT_SUB_ID/resourceGroups/$RG"

# Assign necessary roles to the User-Assigned Managed Identity
# this cannot be run because require tenant level permissions
az role assignment create --assignee-object-id $UAMI_PRINCIPAL_ID --assignee-principal-type ServicePrincipal --role "AcrPush" --scope $SCOPE
az role assignment create --assignee-object-id $UAMI_PRINCIPAL_ID --assignee-principal-type ServicePrincipal --role "Contributor" --scope $SCOPE
az role assignment create --assignee-object-id $UAMI_PRINCIPAL_ID --assignee-principal-type ServicePrincipal --role "Key Vault Secrets User" --scope $SCOPE


# per repo
CREDENTIAL_NAME="gh-dev-payroll-email-agent"
ISSUER_URL="https://token.actions.githubusercontent.com"
SUBJECT="repo:paychex/payroll-email-agent:environment:dev"

az identity federated-credential create \
  --name "$CREDENTIAL_NAME" \
  --identity-name "$UAMI_NAME" \
  --resource-group "$RG" \
  --issuer "$ISSUER_URL" \
  --subject "$SUBJECT" \
  --audiences "api://AzureADTokenExchange"