services:
  payroll-agent:
    build: .
    ports:
      - "8000:8000"
    env_file:
      - .env
    restart: on-failure
    volumes:
      - .:/app
      # Exclude the virtual environment from being overwritten
      - /app/.venv
    environment:
      - MCP_SERVERS__PAYCHEX__URL=http://paychex_payroll_mcp-server:9000/mcp
      - MCP_SERVERS__PAYCHEX__TRANSPORT=streamable_http
      - MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL=http://paychex_payroll_mcp-server:9000/auth/token
      - MCP_SERVERS__PAYCHEX__CLIENT_ID=payroll_agent
      - MCP_SERVERS__PAYCHEX__CLIENT_SECRET=payroll_agent_secret
      - PYTHONPATH=/app
      - UV_LINK_MODE=copy
      - UV_COMPILE_BYTECODE=1
      - WATCHFILES_FORCE_POLLING=true
    working_dir: /app
    entrypoint: []
    command: python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --reload-dir /app
    networks:
      - payroll-shared-network

  langgraph-playground:
    build: .
    ports:
      - "2024:2024"
    env_file:
      - .env
    restart: on-failure
    volumes:
      - .:/app
      - /app/.venv
    environment:
      - MCP_SERVERS__PAYCHEX__URL=http://paychex_payroll_mcp-server:9000/mcp
      - MCP_SERVERS__PAYCHEX__TRANSPORT=streamable_http
      - MCP_SERVERS__PAYCHEX__AUTHENTICATION_URL=http://paychex_payroll_mcp-server:9000/auth/token
      - MCP_SERVERS__PAYCHEX__CLIENT_ID=payroll_agent
      - MCP_SERVERS__PAYCHEX__CLIENT_SECRET=payroll_agent_secret
      - PYTHONPATH=/app
      - UV_LINK_MODE=copy
      - UV_COMPILE_BYTECODE=1
      - WATCHFILES_FORCE_POLLING=true
    working_dir: /app
    entrypoint: []
    # Install langgraph-cli and run the playground
    command: uvx --refresh --from "langgraph-cli[inmem]" --with-editable . --python 3.11 langgraph dev --host 0.0.0.0 --port 2024
    networks:
      - payroll-shared-network

networks:
  payroll-shared-network:
    name: payroll-shared-network
    external: true